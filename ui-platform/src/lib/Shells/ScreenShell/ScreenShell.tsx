import Keycloak from 'keycloak-js';
import { useEffect, useMemo } from 'react';
import { createPortal } from 'react-dom';
import styled from 'styled-components';
import { AlertModal } from '../../Components/AlertModal/AlertModal';
import { Loader } from '../../Components/Loader/Loader';
import { ScrollableContent } from '../../Components/Scrollbar/Scrollbar';
import { ActionConfig, useResetOnComponentUnmount } from '../../Engine';
import { ClientConfigHandler } from '../../Engine/components/ClientConfigHandler';
import { ClientDynamicScreenLoader } from '../../Engine/components/ClientDynamicScreenLoader';
import { EventHandler } from '../../Engine/components/EventHandler';
import { useAsyncLoaderStore } from '../../Engine/hooks/useAsyncLoaderStore';
import {
  ModalState,
  setModalState,
  useModalStore,
} from '../../Engine/hooks/useModalStore';
import { ScreenConfig } from '../../Engine/models/screen.config';
import { useAppStore } from '../../Engine/useAppStore';
import { componentMap } from '../../component-map';
import { ScreenShellNavigationWrapper } from './ScreenShellNavigationWrapper';
import React from 'react';

const ModuleContent = styled.div`
  background: ${(props) => props.theme.ColorsBackgroundModule};
  padding-top: ${(props) => props.theme.SpacingXxl};
  padding-left: ${(props) => props.theme.SpacingLg};
  padding-right: ${(props) => props.theme.SpacingLg};
  /* padding-bottom: 4rem; */
  box-sizing: border-box;
  border-radius: 0 7px 7px 7px;
  grid-area: Module-Content;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
`;

const StickyHeader = styled.div`
  position: absolute;
  top: 0;
  padding-top: ${(props) => props.theme.SpacingLg};
  padding-bottom: ${(props) => props.theme.SpacingLg};
  width: calc(100% - 3.5 * ${(props) => props.theme.SpacingLg});
  display: grid;
  grid-auto-flow: row;
  justify-items: center;
  background: linear-gradient(0deg, #262728 0%, rgba(38, 39, 40, 0) 95%);
  backdrop-filter: blur(4px);
  min-height: max-content;
  z-index: 1;
`;

const ContentWitDiv = styled.div<{ children: any }>`
  max-height: 100% !important;
  padding-right: ${(props) => props.theme.SpacingXl};
  padding-bottom: 4rem;
  box-sizing: border-box;
  height: calc(100vh - 2rem - 72px);
  overflow: auto;
`;

const ContentWithScrollableContent = styled(ScrollableContent)<{
  children: any;
}>`
  max-height: 100% !important;
  padding-right: ${(props) => props.theme.SpacingXl};
  padding-bottom: 4rem;
  box-sizing: border-box;
  height: calc(100vh - 2rem - 72px);
  overflow: auto;

  & > .simplebar-track.simplebar-vertical {
    margin-bottom: 4rem;
    height: calc(100% - 4rem);
  }
`;

const Content = ({
  useScrollableContent = true,
  children,
}: {
  useScrollableContent?: boolean;
  children: any;
}) => {
  const Container = () => {
    if (!useScrollableContent) return <ContentWitDiv>{children}</ContentWitDiv>;
    return (
      <ContentWithScrollableContent>{children}</ContentWithScrollableContent>
    );
  };
  return Container();
};

type Props = {
  screenConfig: ScreenConfig;
  clientDataObject: any;
  isStory?: boolean;
  fetcher?: any;
  submit?: any;
  navigation?: any;
  envObject?: any;
  actionData?: any;
  callClientAction: (config: ActionConfig) => void;
  keycloak?: Keycloak;
  persistKeys?: string[];
  enableCleanup?: boolean;
};

/**
 * Renders a screen shell component with navigation buttons and content.
 *
 * @param {{layout?: React.CSSProperties;
 *   onEnter?: ActionConfig[];
 *   onLeave?: ActionConfig[];
 *   fetchCalls?: FetchConfig[];
 *   fragments: FragmentConfig[];
 *   navs: FlowNavConfig[];}} screenConfig - The configuration object for the screen.
 * @param {object} clientDataObject - The data object for the client.
 * @param {any} router - The router object i.e. react-router or next-router for managing navigation.
 * @return {JSX.Element} The rendered screen shell component.
 */
export function ScreenShell({
  screenConfig,
  clientDataObject,
  isStory,
  fetcher,
  submit,
  navigation,
  envObject,
  actionData,
  callClientAction,
  keycloak,
  persistKeys,
  enableCleanup,
}: Props) {
  console.log({ componentMap });

  const currentModalConfig = useModalStore(
    (state: Partial<ModalState>) => state.currentModalConfig
  );

  const setState = useAppStore((state: any) => state.setState);
  const asyncLoading = useAsyncLoaderStore((state: any) => state.asyncLoading);

  const fetcherData = useMemo(() => {
    return fetcher?.data?.fetchResultsObject || {};
  }, [fetcher.data]);

  // useEffect(() => {
  //   // console.log({fetchData: fetcher.data})
  //   if (fetcher.data) {
  //     // console.log('RESETTING THE POST DATA')
  //     setState((state: any) => ({ postData: {} }));
  //   }
  // }, [fetcher])

  useEffect(() => {
    // console.log({fetchData: fetcher.data})
    if (navigation.state === 'idle') {
      console.log('RESETTING THE POST DATA');
      setState((state: any) => ({ postData: {} }));
    }
  }, [navigation]);

  // Translate FlowNavConfig to ModuleButton
  console.log({ navigation });

  const headerConfig = useMemo(() => {
    return {
      ...screenConfig,
      fragments: screenConfig.fragments.filter(
        (fragment) => fragment?.isStickyHeader
      ),
    };
  }, [screenConfig]);

  const bodyConfig = useMemo(() => {
    return {
      ...screenConfig,
      fragments: screenConfig.fragments.filter(
        (fragment) => !fragment?.isStickyHeader
      ),
    };
  }, [screenConfig]);

  const persistedKeys = persistKeys || [];
  const stateName = useMemo(() => screenConfig?.id, [screenConfig]);
  useResetOnComponentUnmount(persistedKeys, {
    enableCleanup,
    softReset: true,
    referenceDep: stateName,
  });

  return (
    <ModuleContent data-testid="screen-shell-module-content">
      {fetcher.state !== 'idle' && <Loader type="alert" />}
      {navigation.state !== 'idle' && <Loader type="alert" />}
      {asyncLoading && <Loader type="alert" />}
      <ClientConfigHandler
        callClientAction={callClientAction}
        levelConfig={screenConfig}
        clientDataObject={{ ...clientDataObject, ...fetcherData }}
      />
      <EventHandler
        eventConfigs={screenConfig.eventConfigs}
        callClientAction={callClientAction}
        clientDataObject={{ ...clientDataObject, ...fetcherData }}
      />
      {currentModalConfig?.display &&
        createPortal(
          <AlertModal
            componentMap={componentMap}
            setModalState={setModalState}
            {...{
              heading: currentModalConfig.heading,
              headingLevel: currentModalConfig.headingLevel,
              headingType: currentModalConfig.headingType,
              type: currentModalConfig.type,
              display: currentModalConfig.display,
              layout: currentModalConfig.layout,
              onEnter: currentModalConfig.onEnter,
              onLeave: currentModalConfig.onLeave,
              fetchCalls: currentModalConfig.fetchCalls,
              fragments: currentModalConfig.fragments,
              navs: currentModalConfig.navs,
            }}
            onClose={() => {
              currentModalConfig?.onClose &&
                callClientAction(currentModalConfig.onClose);
              setModalState({ display: false });
            }}
            submit={submit as any}
            fetcher={fetcher as any}
            callClientAction={callClientAction}
          />,
          document.body
        )}
      <StickyHeader style={headerConfig.layout}>
        {
          <ClientDynamicScreenLoader
            keycloak={keycloak}
            callClientAction={callClientAction}
            componentMap={componentMap}
            config={headerConfig}
            navigation={navigation}
            submit={submit as any}
            fetcher={fetcher as any}
            actionData={actionData}
          />
        }
      </StickyHeader>
      <Content
        data-testid="screen-shell-content"
        useScrollableContent={screenConfig?.useScrollable}
      >
        <ClientDynamicScreenLoader
          callClientAction={callClientAction}
          componentMap={componentMap}
          config={bodyConfig}
          keycloak={keycloak}
          navigation={navigation}
          submit={submit as any}
          fetcher={fetcher as any}
          actionData={actionData}
        />
      </Content>

      <ScreenShellNavigationWrapper
        callClientAction={callClientAction}
        isStory={isStory}
        screenConfig={screenConfig}
        submit={submit as any}
        fetcher={fetcher as any}
        envObject={envObject}
      />
    </ModuleContent>
  );
}
