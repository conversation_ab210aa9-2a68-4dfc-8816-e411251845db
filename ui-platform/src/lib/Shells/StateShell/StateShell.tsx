'use client';
import Keycloak from 'keycloak-js';
import React, {
  ComponentPropsWithRef,
  ReactNode,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import styled from 'styled-components';
import { componentMap } from '../../component-map';
import { ActionPanel } from '../../Components/ActionPanel/ActionPanel/ActionPanel';
import { useActionPanelStore } from '../../Components/ActionPanel/hooks/useActionPanel';
import { useResetOnComponentUnmount } from '../../Engine';
import { ClientConfigHandler } from '../../Engine/components/ClientConfigHandler';
import { EventHandler } from '../../Engine/components/EventHandler';
import { FormListener } from '../../Engine/components/FormListener';
import { renderTemplate } from '../../Engine/helpers/render-template';
import { templateFunctions } from '../../Engine/helpers/render-template-functions';
import { ActionConfig, FragmentConfig, StateConfig } from '../../Engine/models';
import { InputControlConfig } from '../../Fragments/form-builder/types/input-control.config';

const ContentsContainer = styled.div<
  {
    templateAreas: string;
    templateColumns: string;
    isOverlayVisible: boolean;
  } & ComponentPropsWithRef<'div'>
>`
  display: grid;
  grid-template-areas: '${(props) => props.templateAreas}';
  grid-template-columns: ${(props) => props.templateColumns};
  grid-template-rows: 1fr;
  grid-column-gap: ${(props) => props.theme.SpacingLg};
  height: 100%;
  width: ${(props) => (props.isOverlayVisible ? 'calc(100% - 326px)' : '100%')};
  transition: width 1s ease-in-out;
  box-sizing: border-box;
  grid-area: Body-Content;
  height: ${(props) => `calc(100vh - 53px - ${props.theme.SpacingLg})`};
`;

const ActionPanelArea = styled.div<{
  isOverlayVisible: boolean;
}>`
  grid-area: Action-Panel;
  width: ${(props) => (props.isOverlayVisible ? 'calc(56px + 326px)' : '56px')};
  transition: width 1s ease-in-out;
  position: relative;
  // z-index: 0;
`;

interface Props {
  children: ReactNode;
  stateConfig: StateConfig;
  clientDataObject: any;
  fakeUseNavigation?: { state: string };
  fetcher?: any;
  submit?: any;
  callClientAction: (config: ActionConfig) => void;
  isStory?: boolean;
  keycloak?: Keycloak;
  persistKeys?: string[];
  enableCleanup?: boolean;
  searchProps?: {
    searchUrl: string;
    token: string;
    tokenPrefix: string;
    onSearchTrigger?: (searchTerm: string) => void;
  };
}

/**
 * Renders a StateShell component with the given state configuration, client data object, and children.
 *
 * @param {Props} props - The props object containing the following properties:
 *   - stateConfig: The state configuration object.
 *   - clientDataObject: The client data object.
 *   - children: The children components.
 * @return {JSX.Element} The rendered StateShell component.
 */
export const StateShell = React.memo(
  ({
    stateConfig,
    clientDataObject,
    children,
    fakeUseNavigation,
    fetcher,
    submit,
    keycloak,
    callClientAction,
    persistKeys,
    enableCleanup,
    searchProps,
  }: Props) => {
    const actionPanelConfigs = stateConfig.actionPanels || [];
    const activeActionPanelView = stateConfig.activeActionPanelView;

    const controlsDefaultValues: any = useMemo(
      () =>
        Object.values(stateConfig.screens)
          .reduce((acc: FragmentConfig[], screen) => {
            return [...acc, ...screen.fragments];
          }, [])
          .filter((frag) => {
            return (
              frag.component === 'FormBuilder' || frag.component === 'ButtonRow'
            );
          })
          .reduce((acc: InputControlConfig[], fragment) => {
            // console.log({ acc });
            if (fragment.component === 'FormBuilder') {
              return [...acc, ...(fragment.props?.config?.controls || [])];
            } else if (fragment.component === 'ButtonRow') {
              const frags = fragment.props?.buttons?.reduce(
                (butAcc: any, but: any) => {
                  const validClicks = but?.onClick.filter(
                    (c: any) => c.action === 'triggerModal'
                  );
                  const validFragments = validClicks.reduce(
                    (vAcc: any, vc: any) => [
                      ...vAcc,
                      ...vc.payload[0].fragments.filter(
                        (f: any) => f.component === 'FormBuilder'
                      ),
                    ],
                    []
                  );
                  return [...butAcc, ...validFragments];
                },
                []
              );
              return [
                ...acc,
                ...frags.flatMap((f: any) => f.props?.config?.controls || []),
              ];
            } else {
              return acc;
            }
          }, [])
          .reduce((acc, control) => {
            return {
              ...acc,
              [control.name]: '',
            };
          }, {}),
      [stateConfig]
    );

    // console.log({ controlsDefaultValues });

    const [processedTitle, setProcessedTitle] = useState<string>(''); // Process title template when stateConfig or clientDataObject changes
    useEffect(() => {}, [stateConfig, clientDataObject]);

    // FORM HANDLER
    const methods = useForm({
      mode: 'onChange',
      defaultValues: controlsDefaultValues,
    });

    // Reset Form
    useEffect(() => {
      console.log('resetting form!');
      methods.reset(controlsDefaultValues);
    }, [stateConfig]);

    const persistedKeys = persistKeys || [];
    const stateName = useMemo(() => stateConfig.title.template, [stateConfig]);
    useResetOnComponentUnmount(persistedKeys, {
      enableCleanup,
      softReset: true,
      referenceDep: stateName,
    });

    const onSubmit = (data: any) => {
      console.log(data);
      // (functionMapper as {[id: string]: any})['check'](data);
    };

    const gridTemplateAreas = useMemo(
      () =>
        actionPanelConfigs.length
          ? 'Module-Content Action-Panel'
          : 'Module-Content',
      [actionPanelConfigs]
    );
    const gridTemplateColumns = useMemo(
      () => (actionPanelConfigs.length ? '1fr 56px' : '1fr'),
      [actionPanelConfigs]
    );

    const { isOverlayVisible } = useActionPanelStore();
    return (
      <FormProvider {...methods}>
        <FormListener
          stateConfig={stateConfig}
          clientDataObject={clientDataObject}
        />
        <ClientConfigHandler
          callClientAction={callClientAction}
          levelConfig={stateConfig}
          clientDataObject={clientDataObject}
        />
        <EventHandler
          eventConfigs={stateConfig.eventConfigs}
          callClientAction={callClientAction}
          clientDataObject={clientDataObject}
        />
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <ContentsContainer
            templateAreas={gridTemplateAreas}
            templateColumns={gridTemplateColumns}
            isOverlayVisible={isOverlayVisible}
          >
            {children}

            {!!actionPanelConfigs.length && (
              <ActionPanelArea isOverlayVisible={isOverlayVisible}>
                <ActionPanel
                  callClientAction={callClientAction}
                  componentMap={componentMap}
                  configs={actionPanelConfigs}
                  activeView={activeActionPanelView}
                  {...{ fetcher, submit }}
                  searchProps={searchProps}
                />
              </ActionPanelArea>
            )}
          </ContentsContainer>
        </form>
      </FormProvider>
    );
  }
);
