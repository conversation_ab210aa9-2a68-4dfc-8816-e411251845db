import { ComponentPropsWithRef, ReactNode, useEffect, useMemo } from 'react';
import styled from 'styled-components';
// import {
//   ActionPanel,
//   // ActionPanelProps,
//   Loader,
//   ScrollableContent,
//   // useListPagination2,
// } from '../../Components';
import { WorkflowTypeConfig } from '../../Engine/models/workflow-type.config';
// import { Pagination } from '../../Fragments';
import { Navigation, useNavigation } from 'react-router-dom';
import { ActionPanel } from '../../Components/ActionPanel/ActionPanel/ActionPanel';
import { useActionPanelStore } from '../../Components/ActionPanel/hooks/useActionPanel';
import { Loader } from '../../Components/Loader/Loader';
import { ScrollableContent } from '../../Components/Scrollbar/Scrollbar';
import { ActionConfig } from '../../Engine';
import { ClientConfigHandler } from '../../Engine/components/ClientConfigHandler';
import { Pagination } from '../../Fragments/Pagination/Pagination';

const ContentsContainer = styled.div<
  {
    templateAreas: string;
    templateColumns: string;
    isOverlayVisible: boolean;
  } & ComponentPropsWithRef<'div'>
>`
  display: grid;
  grid-template-areas: '${(props) => props.templateAreas}';
  grid-template-columns: ${(props) => props.templateColumns};
  grid-template-rows: 1fr;
  grid-column-gap: ${(props) => props.theme.SpacingLg};
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  grid-area: Body-Content;
  height: ${(props) => `calc(100vh - 53px - ${props.theme.SpacingLg})`};
  width: ${(props) => (props.isOverlayVisible ? 'calc(100% - 326px)' : '100%')};
  transition: width 1s ease-in-out;
`;

const WorkflowTypeShellPagination = styled(Pagination)`
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
`;

const ModuleContent = styled.div`
  background: ${(props) => props.theme.ColorsBackgroundModule};
  padding-top: 2rem;
  padding-left: 1rem;
  padding-right: 1rem;
  box-sizing: border-box;
  border-radius: 0 7px 7px 7px;
  grid-area: Module-Content;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
`;

const ActionPanelArea = styled.div<{
  isOverlayVisible: boolean;
}>`
  grid-area: Action-Panel;
  width: ${(props) => (props.isOverlayVisible ? 'calc(56px + 326px)' : '56px')};
  transition: width 1s ease-in-out;
  position: relative;
  // z-index: 0;
`;

const Content = styled(ScrollableContent)<{
  items?: any[];
  scrollable?: boolean;
}>`
  max-height: 100% !important;
  padding-right: ${(props) => props.theme.SpacingXl};

  ${(props) =>
    props?.scrollable || (props?.items && props?.items.length > 0)
      ? 'padding-bottom: 4rem; box-sizing: border-box'
      : ''};
  ${(props) => (!props?.scrollable ? 'overflow: hidden;' : '')};
`;

interface Props<T extends object> {
  children: ReactNode;
  mockItems?: any[];
  workflowTypeConfig: WorkflowTypeConfig;
  clientDataObject: any;
  scrollable?: boolean;
  componentMap: any;
  callClientAction: (config: ActionConfig) => void;
  navigation?: Navigation;
  searchProps?: {
    searchUrl: string;
    token: string;
    tokenPrefix: string;
    onSearchTrigger?: (searchTerm: string) => void;
  };
}

/**
 * Renders a WorkflowTypeShell component.
 *
 * @param {Props<T>} props - The props object containing the following properties:
 *   - workflowTypeConfig: The configuration for the workflow type.
 *   - clientDataObject: The client data object.
 *   - children: The child components.
 * @return {JSX.Element} The rendered WorkflowTypeShell component.
 */
export function WorkflowTypeShell<T extends object>({
  workflowTypeConfig,
  clientDataObject,
  children,
  componentMap,
  callClientAction,
  navigation,
  searchProps,
}: Props<T>) {
  const actionPanelConfigs = workflowTypeConfig.actionPanels || [];
  const { isOverlayVisible } = useActionPanelStore();

  const gridTemplateAreas = useMemo(
    () =>
      actionPanelConfigs.length
        ? 'Module-Content Action-Panel'
        : 'Module-Content',
    [actionPanelConfigs]
  );
  const gridTemplateColumns = useMemo(
    () => (actionPanelConfigs.length ? '1fr 56px' : '1fr'),
    [actionPanelConfigs]
  );

  return (
    <>
      {navigation?.state === 'loading' && <Loader type="alert" />}
      <ClientConfigHandler
        callClientAction={callClientAction}
        levelConfig={workflowTypeConfig}
        clientDataObject={clientDataObject}
      />
      <ContentsContainer
        data-testid="workflow-type-shell-contents-container"
        templateAreas={gridTemplateAreas}
        templateColumns={gridTemplateColumns}
        isOverlayVisible={isOverlayVisible}
      >
        {children}

        {!!actionPanelConfigs.length && (
          <ActionPanelArea isOverlayVisible={isOverlayVisible}>
            <ActionPanel
              callClientAction={callClientAction}
              configs={actionPanelConfigs}
              componentMap={componentMap}
              searchProps={searchProps}
            />
          </ActionPanelArea>
        )}
      </ContentsContainer>
    </>
  );
}
