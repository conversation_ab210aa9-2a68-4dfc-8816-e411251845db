import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { PerformanceMetrics } from '../types';

/**
 * Hook for monitoring and optimizing component performance
 */
export const useRenderCount = (componentName: string) => {
  const renderCount = useRef(0);
  renderCount.current += 1;

  if (process.env.NODE_ENV === 'development') {
    console.log(`${componentName} rendered ${renderCount.current} times`);
  }

  return renderCount.current;
};

/**
 * Hook for tracking detailed performance metrics
 */
export const usePerformanceMetrics = (enabled = false) => {
  const metricsRef = useRef<PerformanceMetrics>({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0,
    totalRenderTime: 0,
  });

  const startTime = useRef(0);

  useEffect(() => {
    if (!enabled) return;

    startTime.current = performance.now();
    metricsRef.current.renderCount += 1;
  });

  useEffect(() => {
    if (!enabled) return;

    const endTime = performance.now();
    const renderTime = endTime - startTime.current;

    metricsRef.current.lastRenderTime = renderTime;
    metricsRef.current.totalRenderTime += renderTime;
    metricsRef.current.averageRenderTime =
      metricsRef.current.totalRenderTime / metricsRef.current.renderCount;

    if (process.env.NODE_ENV === 'development') {
      console.log('Render metrics:', {
        renderCount: metricsRef.current.renderCount,
        lastRenderTime: renderTime.toFixed(2) + 'ms',
        averageRenderTime:
          metricsRef.current.averageRenderTime.toFixed(2) + 'ms',
      });
    }
  });

  return metricsRef.current;
};

/**
 * Hook for debugging why a component re-rendered
 */
export const useWhyDidYouUpdate = (
  name: string,
  props: Record<string, any>
) => {
  const previousProps = useRef<Record<string, any>>();

  useEffect(() => {
    if (previousProps.current && process.env.NODE_ENV === 'development') {
      const allKeys = Object.keys({ ...previousProps.current, ...props });
      const changedProps: Record<string, { from: any; to: any }> = {};

      allKeys.forEach((key) => {
        if (previousProps.current![key] !== props[key]) {
          changedProps[key] = {
            from: previousProps.current![key],
            to: props[key],
          };
        }
      });

      if (Object.keys(changedProps).length) {
        console.log('[why-did-you-update]', name, changedProps);
      }
    }

    previousProps.current = props;
  });
};

/**
 * Hook for debouncing rapid state updates
 */
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Hook for batching multiple state updates
 */
export const useBatchedUpdates = () => {
  const pendingUpdates = useRef<(() => void)[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const batchUpdate = useCallback((updateFn: () => void) => {
    pendingUpdates.current.push(updateFn);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      const updates = pendingUpdates.current;
      pendingUpdates.current = [];

      // Execute all batched updates
      updates.forEach((update) => update());
    }, 16); // Next frame
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return { batchUpdate };
};

/**
 * Hook for optimizing expensive computations with memoization
 */
export const useStableMemo = <T>(
  factory: () => T,
  deps: React.DependencyList,
  isEqual?: (a: T, b: T) => boolean
): T => {
  const valueRef = useRef<T>();
  const depsRef = useRef<React.DependencyList>();

  const hasChanged = useMemo(() => {
    if (!depsRef.current) return true;

    return deps.some((dep, index) => {
      const prevDep = depsRef.current![index];
      return isEqual ? !isEqual(dep, prevDep) : dep !== prevDep;
    });
  }, deps);

  if (hasChanged || valueRef.current === undefined) {
    valueRef.current = factory();
    depsRef.current = deps;
  }

  return valueRef.current;
};

/**
 * Hook for preventing unnecessary re-renders with stable callbacks
 */
export const useStableCallback = <T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T => {
  const callbackRef = useRef<T>(callback);
  const depsRef = useRef<React.DependencyList>(deps);

  // Update callback if dependencies changed
  const hasChanged = deps.some((dep, index) => dep !== depsRef.current[index]);

  if (hasChanged) {
    callbackRef.current = callback;
    depsRef.current = deps;
  }

  return useCallback((...args: Parameters<T>) => {
    return callbackRef.current(...args);
  }, []) as T;
};
