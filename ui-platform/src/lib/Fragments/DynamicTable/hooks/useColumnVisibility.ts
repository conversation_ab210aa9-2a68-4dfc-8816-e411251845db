import { useCallback, useMemo, useState } from 'react';
import { VisibilityState } from '@tanstack/react-table';
import { ColumnConfig, ColumnVisibilityConfig } from '../types';

/**
 * Enhanced column visibility hook with data access management
 * Handles column visibility state while maintaining data accessibility for hidden columns
 */
export const useColumnVisibility = <T extends Record<string, any>>(
  data: T[],
  columnConfig: Partial<Record<keyof T, ColumnConfig<T>>>,
  columnVisibilityConfig?: ColumnVisibilityConfig<T>,
  displayColumns?: Partial<keyof T>[]
) => {
  // Initialize column visibility state
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(() => {
    const initialVisibility: VisibilityState = {};
    
    // Handle legacy displayColumns prop
    if (displayColumns) {
      for (const key in columnConfig) {
        initialVisibility[key] = displayColumns.includes(key as Partial<keyof T>);
      }
      return initialVisibility;
    }
    
    // Handle new columnVisibilityConfig
    if (columnVisibilityConfig) {
      // Start with all columns visible by default
      if (data.length > 0) {
        Object.keys(data[0]).forEach(key => {
          initialVisibility[key] = true;
        });
      }
      
      // Apply visible columns if specified
      if (columnVisibilityConfig.visibleColumns) {
        Object.keys(initialVisibility).forEach(key => {
          initialVisibility[key] = columnVisibilityConfig.visibleColumns?.includes(key as keyof T) ?? false;
        });
      }
      
      // Apply hidden columns if specified
      if (columnVisibilityConfig.hiddenColumns) {
        columnVisibilityConfig.hiddenColumns.forEach(key => {
          initialVisibility[key as string] = false;
        });
      }
      
      // Apply dynamic visibility if specified
      if (columnVisibilityConfig.dynamicVisibility) {
        const dynamicVisible = columnVisibilityConfig.dynamicVisibility(data);
        Object.keys(initialVisibility).forEach(key => {
          if (!dynamicVisible.includes(key as keyof T)) {
            initialVisibility[key] = false;
          }
        });
      }
    }
    
    return initialVisibility;
  });

  // Track hidden columns that should maintain data access
  const hiddenColumnsWithDataAccess = useMemo(() => {
    const hiddenWithAccess: string[] = [];
    
    if (columnVisibilityConfig?.maintainDataAccess) {
      Object.entries(columnVisibility).forEach(([key, visible]) => {
        if (!visible) {
          const config = columnConfig[key as keyof T];
          if (config?.controlType || config?.visibilityConfig?.maintainDataAccess !== false) {
            hiddenWithAccess.push(key);
          }
        }
      });
    }
    
    return hiddenWithAccess;
  }, [columnVisibility, columnVisibilityConfig, columnConfig]);

  // Function to toggle column visibility
  const toggleColumnVisibility = useCallback((columnId: string, visible?: boolean) => {
    setColumnVisibility(prev => ({
      ...prev,
      [columnId]: visible !== undefined ? visible : !prev[columnId]
    }));
  }, []);

  // Function to toggle group visibility
  const toggleGroupVisibility = useCallback((groupName: string, visible: boolean) => {
    if (columnVisibilityConfig?.groupVisibility?.[groupName]) {
      const updates: Record<string, boolean> = {};
      columnVisibilityConfig.groupVisibility[groupName].forEach(columnKey => {
        updates[columnKey as string] = visible;
      });
      
      setColumnVisibility(prev => ({ ...prev, ...updates }));
    }
  }, [columnVisibilityConfig]);

  // Function to get visible columns for a specific row (considering conditional visibility)
  const getVisibleColumnsForRow = useCallback((row: T, rowIndex: number) => {
    const visibleColumns: string[] = [];
    
    Object.entries(columnVisibility).forEach(([key, visible]) => {
      if (visible) {
        const config = columnConfig[key as keyof T];
        
        // Check conditional visibility
        if (config?.visibilityConfig?.conditionalVisibility) {
          const isConditionallyVisible = config.visibilityConfig.conditionalVisibility(row, rowIndex);
          if (isConditionallyVisible) {
            visibleColumns.push(key);
          }
        } else {
          visibleColumns.push(key);
        }
      }
    });
    
    return visibleColumns;
  }, [columnVisibility, columnConfig]);

  // Function to check if a column should maintain data access when hidden
  const shouldMaintainDataAccess = useCallback((columnKey: string) => {
    if (!columnVisibilityConfig?.maintainDataAccess) return false;
    
    const config = columnConfig[columnKey as keyof T];
    return config?.controlType || config?.visibilityConfig?.maintainDataAccess !== false;
  }, [columnVisibilityConfig, columnConfig]);

  return {
    columnVisibility,
    setColumnVisibility,
    hiddenColumnsWithDataAccess,
    toggleColumnVisibility,
    toggleGroupVisibility,
    getVisibleColumnsForRow,
    shouldMaintainDataAccess,
  };
};
