import React from 'react';
import { ActionConfig } from '../../../Engine/models/action.config';
import { TextConfig } from '../../../Engine/models/text.config';
import { InputControlConfig } from '../../form-builder/types/input-control.config';

// Enhanced control types based on FormBuilder capabilities
export type TableControlType =
  | 'checkbox'
  | 'plain-text'
  | 'textarea'
  | 'single-select'
  | 'multi-select'
  | 'radio-group'
  | 'datepicker'
  | 'date-and-time-picker'
  | 'timepicker'
  | 'add-file'
  | 'custom';

// Enhanced column configuration with visibility and data access controls
export interface ColumnConfig<T> {
  key: keyof T;
  header?: string | TextConfig;
  cell?: (
    value: any,
    row: T,
    rowIndex: number,
    onChange?: (val: any) => void
  ) => React.ReactNode;
  filterable?: boolean;
  sortable?: boolean;
  resizable?: boolean;
  width?: string | number;
  minWidth?: number;
  maxWidth?: number;
  headerTextConfig?: TextConfig;
  footerTextConfig?: TextConfig;
  bodyTextConfig?: (value: any, row: T) => TextConfig;
  controlType?: TableControlType;
  controlConfig?: Partial<InputControlConfig>;
  controlProps?: Record<string, any>;
  onControlChange?:
    | ((value: any, row: T, rowIndex: number) => void)
    | ActionConfig[];
  customControlName?: string;
  sticky?: 'left' | 'right';
  hidden?: boolean;
  exportable?: boolean;
  validator?: (value: any) => string | null;
  formatter?: (value: any) => string;
  aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max' | 'custom';
  customAggregation?: (values: any[]) => any;
  _callClientAction?: (config: ActionConfig[]) => void;
  
  // Enhanced visibility controls
  visibilityConfig?: {
    defaultVisible?: boolean;
    maintainDataAccess?: boolean; // Keep data accessible even when hidden
    conditionalVisibility?: (data: T[], rowIndex?: number) => boolean;
    groupVisibility?: string; // Group columns for bulk visibility control
  };
}

// Row action configuration
export interface RowAction<T> {
  id: string;
  label: string;
  icon?: string;
  onClick: (row: T, rowIndex: number) => void;
  disabled?: (row: T) => boolean;
  hidden?: (row: T) => boolean;
  variant?: 'primary' | 'secondary' | 'danger' | 'success';
}

// Bulk action configuration
export interface BulkAction<T> {
  id: string;
  label: string;
  icon?: string;
  onClick: (selectedRows: T[], selectedIndices: number[]) => void;
  disabled?: (selectedRows: T[]) => boolean;
  variant?: 'primary' | 'secondary' | 'danger' | 'success';
}

// Export configuration
export interface ExportConfig {
  enabled: boolean;
  formats?: ('csv' | 'excel' | 'pdf')[];
  filename?: string;
  customExporter?: (data: any[], format: string) => void;
}

// Virtual scrolling configuration
export interface VirtualScrollConfig {
  enabled: boolean;
  itemHeight?: number;
  overscan?: number;
  threshold?: number; // Minimum rows to enable virtualization
}

// Loading state configuration
export interface LoadingConfig {
  enabled: boolean;
  skeleton?: boolean;
  rows?: number;
  message?: string;
}

// Error handling configuration
export interface ErrorConfig {
  boundary?: boolean;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error) => void;
}

// CRUD operations configuration
export interface CrudConfig<T> {
  enabled: boolean;
  create?: {
    enabled: boolean;
    onAdd?: (newRow: Partial<T>) => Promise<T> | T;
    defaultValues?: Partial<T>;
    validation?: (row: Partial<T>) => Record<string, string> | null;
  };
  update?: {
    enabled: boolean;
    onUpdate?: (rowId: string | number, updates: Partial<T>) => Promise<T> | T;
    optimistic?: boolean; // Update UI immediately before server response
    validation?: (row: T, updates: Partial<T>) => Record<string, string> | null;
  };
  delete?: {
    enabled: boolean;
    onDelete?: (rowId: string | number) => Promise<void> | void;
    confirmation?: boolean | ((row: T) => string);
    softDelete?: boolean; // Mark as deleted instead of removing
  };
  bulkOperations?: {
    enabled: boolean;
    onBulkUpdate?: (
      rowIds: (string | number)[],
      updates: Partial<T>
    ) => Promise<T[]> | T[];
    onBulkDelete?: (rowIds: (string | number)[]) => Promise<void> | void;
  };
}

// Enhanced column visibility configuration
export interface ColumnVisibilityConfig<T> {
  // Basic visibility control
  visibleColumns?: (keyof T)[];
  hiddenColumns?: (keyof T)[];
  
  // Advanced visibility options
  maintainDataAccess?: boolean; // Keep hidden column data in form state
  dynamicVisibility?: (data: T[]) => (keyof T)[]; // Dynamic column visibility
  groupVisibility?: Record<string, (keyof T)[]>; // Group columns for bulk control
  
  // Conditional visibility per row
  conditionalVisibility?: Record<keyof T, (row: T, rowIndex: number) => boolean>;
  
  // Responsive visibility
  responsiveVisibility?: {
    mobile?: (keyof T)[];
    tablet?: (keyof T)[];
    desktop?: (keyof T)[];
  };
}

// Performance metrics interface
export interface PerformanceMetrics {
  renderCount: number;
  lastRenderTime: number;
  averageRenderTime: number;
  totalRenderTime: number;
}

// Form integration configuration
export interface FormIntegrationConfig {
  enabled: boolean;
  sharedFormState?: boolean;
  onFormSubmit?: (data: Record<string, any>) => void;
  onFormValidation?: (errors: Record<string, string>) => void;
}
