import React, { memo, useCallback } from 'react';
import { Text } from '../../Text';
import { ActionConfig } from '../../../Engine/models/action.config';
import { ColumnConfig } from '../types';
import { OptimizedCheckbox, OptimizedFormControl } from './OptimizedFormControl';

interface CellRendererProps<T extends Record<string, any>> {
  value: any;
  config?: ColumnConfig<T>;
  row: T;
  rowIndex: number;
  _callClientAction?: (config: ActionConfig[]) => void;
}

// Memoized cell renderer for performance optimization
export const CellRenderer = memo(
  <T extends Record<string, any>>({
    value,
    config,
    row,
    rowIndex,
    _callClientAction,
  }: CellRendererProps<T>) => {
    // If a cell render function is provided, use it
    if (config?.cell) {
      return config.cell(
        value,
        row,
        rowIndex,
        config.onControlChange
          ? (val: any) =>
              typeof config.onControlChange === 'function'
                ? config.onControlChange(val, row, rowIndex)
                : _callClientAction &&
                  Array.isArray(config.onControlChange) &&
                  config.onControlChange.length > 0 &&
                  (config.onControlChange satisfies ActionConfig[])
                ? _callClientAction(
                    (config.onControlChange as ActionConfig[]).map((o) => ({
                      ...o,
                      param: { value: val, row, rowIndex },
                    }))
                  )
                : undefined
          : undefined
      );
    }

    // Render form controls if specified
    if (config?.controlType && row) {
      const controlName = config.key as keyof T as string;
      const currentValue = row[config.key];

      // For simple checkbox, use optimized component to avoid FormBuilder overhead
      if (config.controlType === 'checkbox') {
        const headerText =
          typeof config.header === 'string'
            ? config.header
            : typeof config.header === 'object' && config.header?.text
            ? config.header.text
            : controlName;

        // Stable change handler for checkbox
        const handleCheckboxChange = useCallback(
          (checked: boolean) => {
            if (config.onControlChange) {
              if (typeof config.onControlChange === 'function') {
                config.onControlChange(checked, row, rowIndex);
              } else if (_callClientAction && Array.isArray(config.onControlChange)) {
                _callClientAction(
                  config.onControlChange.map((action) => ({
                    ...action,
                    param: { value: checked, row, rowIndex },
                  }))
                );
              }
            }
          },
          [config.onControlChange, row, rowIndex, _callClientAction]
        );

        return (
          <OptimizedCheckbox
            checked={!!currentValue}
            onChange={handleCheckboxChange}
            name={headerText}
            hideLabel={config.controlProps?.hideLabel}
            disabled={config.controlProps?.disabled}
            rowIndex={rowIndex}
          />
        );
      }

      // For other controls, use optimized form control wrapper
      const uniqueControlName = `${controlName}_row_${rowIndex}`;

      return (
        <OptimizedFormControl
          config={config as ColumnConfig<Record<string, any>>}
          currentValue={currentValue}
          row={row}
          rowIndex={rowIndex}
          uniqueControlName={uniqueControlName}
          _callClientAction={_callClientAction}
        />
      );
    }

    // If a bodyTextConfig is provided, use it for Text rendering
    if (config?.bodyTextConfig) {
      const textConfig = config.bodyTextConfig(value, row);
      return <Text textItems={[textConfig]} />;
    }

    // Fallbacks with enhanced type handling
    if (value === null || value === undefined) {
      return (
        <Text
          textItems={[
            {
              text: '-',
              options: {
                format: 'paragraph',
                type: 'value',
              },
            },
          ]}
        />
      );
    }

    if (typeof value === 'boolean') {
      return (
        <Text
          textItems={[
            {
              text: value ? 'Yes' : 'No',
              options: {
                format: 'paragraph',
                type: 'value',
                style: {
                  padding: '2px 8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: 500,
                  background: value ? '#dcfce7' : '#fee2e2',
                  color: value ? '#166534' : '#991b1b',
                },
              },
            },
          ]}
        />
      );
    }

    if (typeof value === 'number') {
      const formattedValue = config?.formatter
        ? config.formatter(value)
        : value.toLocaleString();
      return (
        <Text
          textItems={[
            {
              text: formattedValue,
              options: {
                format: 'paragraph',
                type: 'value',
                style: { fontFamily: 'monospace' },
              },
            },
          ]}
        />
      );
    }

    if (value instanceof Date) {
      const formattedValue = config?.formatter
        ? config.formatter(value)
        : value.toLocaleDateString();
      return (
        <Text
          textItems={[
            {
              text: formattedValue,
              options: {
                format: 'paragraph',
                type: 'value',
                style: { fontFamily: 'monospace' },
              },
            },
          ]}
        />
      );
    }

    if (typeof value === 'string' && value.startsWith('http')) {
      return (
        <Text
          textItems={[
            {
              text: 'Link',
              options: {
                format: 'paragraph',
                type: 'value',
                style: { color: '#2563eb', textDecoration: 'underline' },
              },
            },
          ]}
        />
      );
    }

    if (typeof value === 'object') {
      const displayValue = config?.formatter
        ? config.formatter(value)
        : JSON.stringify(value, null, 2);
      return (
        <Text
          textItems={[
            {
              text: displayValue,
              options: {
                format: 'paragraph',
                type: 'value',
                style: {
                  fontSize: '12px',
                  background: '#f3f4f6',
                  padding: '4px',
                  borderRadius: '4px',
                  maxWidth: '320px',
                  overflow: 'hidden',
                },
              },
            },
          ]}
        />
      );
    }

    const displayValue = config?.formatter
      ? config.formatter(value)
      : String(value);
    return (
      <Text
        textItems={[
          {
            text: displayValue,
            options: {
              format: 'paragraph',
              type: 'value',
            },
          },
        ]}
      />
    );
  },
  (prevProps, nextProps) => {
    // Fast path: if row index changed, always re-render
    if (prevProps.rowIndex !== nextProps.rowIndex) return false;

    // Fast path: if value changed, re-render
    if (prevProps.value !== nextProps.value) return false;

    // Fast path: if config changed, re-render
    if (prevProps.config !== nextProps.config) return false;

    // For control types, check the actual row value for the specific key
    if (prevProps.config?.controlType && nextProps.config?.controlType) {
      const prevRowValue = prevProps.row[prevProps.config.key];
      const nextRowValue = nextProps.row[nextProps.config.key];

      // Only re-render if the specific field value changed
      if (prevRowValue !== nextRowValue) return false;

      // Also check if control type changed
      if (prevProps.config.controlType !== nextProps.config.controlType)
        return false;
    }

    // For cells with custom render functions, do a shallow comparison
    if (prevProps.config?.cell && nextProps.config?.cell) {
      // Re-render if the cell function changed or row data changed
      if (prevProps.config.cell !== nextProps.config.cell) return false;
      if (prevProps.row !== nextProps.row) return false;
    }

    // For text config cells, check if the config changed
    if (prevProps.config?.bodyTextConfig && nextProps.config?.bodyTextConfig) {
      if (prevProps.config.bodyTextConfig !== nextProps.config.bodyTextConfig)
        return false;
    }

    // Default: don't re-render if nothing significant changed
    return true;
  }
);
