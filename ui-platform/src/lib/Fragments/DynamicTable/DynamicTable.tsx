import {
  ColumnDef,
  ColumnFiltersState,
  ColumnSizingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  PaginationState,
  RowSelectionState,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { debounce } from 'lodash';
import React, {
  createContext,
  memo,
  Suspense,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import styled, { css } from 'styled-components';
import SelectionBox from '../../Components/Inputs/CheckBoxes/CheckBoxMolecule/CheckBoxMolecule';
import { ActionConfig } from '../../Engine/models/action.config';
import { TextConfig } from '../../Engine/models/text.config';
import { FormBuilder } from '../form-builder';
import { InputControlConfig } from '../form-builder/types/input-control.config';
import { Text } from '../Text';

// Import modular components and hooks
import { CellRenderer } from './components/CellRenderer';
import { useColumnVisibility } from './hooks/useColumnVisibility';
import {
  usePerformanceMetrics,
  useWhyDidYouUpdate,
} from './hooks/usePerformanceOptimization';
import {
  BulkAction,
  ColumnConfig,
  ColumnVisibilityConfig,
  CrudConfig,
  ErrorConfig,
  ExportConfig,
  FormIntegrationConfig,
  LoadingConfig,
  RowAction,
  TableControlType,
  VirtualScrollConfig,
} from './types';

// Enhanced form state management context with performance optimizations
interface TableFormContextValue {
  formData: Record<string, any>;
  updateFormData: (key: string, value: any) => void;
  batchUpdateFormData: (updates: Record<string, any>) => void;
  getFormValue: (key: string) => any;
  validateField: (key: string, value: any) => string | null;
  isFieldDirty: (key: string) => boolean;
  resetField: (key: string) => void;
  getFieldError: (key: string) => string | null;
}

const TableFormContext = createContext<TableFormContextValue | null>(null);

// Hook to use table form context with performance tracking
const useTableForm = () => {
  const context = useContext(TableFormContext);
  if (!context) {
    throw new Error('useTableForm must be used within a TableFormProvider');
  }
  return context;
};

// Optimized form provider with batched updates and dirty tracking
const TableFormProvider: React.FC<{
  children: React.ReactNode;
  initialData: Record<string, any>;
  onDataChange?: (data: Record<string, any>) => void;
  validators?: Record<string, (value: any) => string | null>;
}> = ({ children, initialData, onDataChange, validators = {} }) => {
  const [formData, setFormData] = useState(initialData);
  const [dirtyFields, setDirtyFields] = useState<Set<string>>(new Set());
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const updateTimeoutRef = useRef<NodeJS.Timeout>();

  // Batched update function to prevent excessive re-renders
  const batchUpdateFormData = useCallback(
    (updates: Record<string, any>) => {
      setFormData((prev) => {
        const newData = { ...prev, ...updates };

        // Clear timeout if pending
        if (updateTimeoutRef.current) {
          clearTimeout(updateTimeoutRef.current);
        }

        // Batch the onChange callback
        updateTimeoutRef.current = setTimeout(() => {
          onDataChange?.(newData);
        }, 16); // Next frame

        return newData;
      });

      // Mark fields as dirty
      setDirtyFields((prev) => {
        const newDirty = new Set(prev);
        Object.keys(updates).forEach((key) => newDirty.add(key));
        return newDirty;
      });
    },
    [onDataChange]
  );

  const updateFormData = useCallback(
    (key: string, value: any) => {
      batchUpdateFormData({ [key]: value });
    },
    [batchUpdateFormData]
  );

  const getFormValue = useCallback(
    (key: string) => {
      return formData[key];
    },
    [formData]
  );

  const validateField = useCallback(
    (key: string, value: any) => {
      const validator = validators[key];
      const error = validator ? validator(value) : null;

      setFieldErrors((prev) => {
        if (error) {
          return { ...prev, [key]: error };
        } else {
          const { [key]: removed, ...rest } = prev;
          return rest;
        }
      });

      return error;
    },
    [validators]
  );

  const isFieldDirty = useCallback(
    (key: string) => {
      return dirtyFields.has(key);
    },
    [dirtyFields]
  );

  const resetField = useCallback(
    (key: string) => {
      setFormData((prev) => ({ ...prev, [key]: initialData[key] }));
      setDirtyFields((prev) => {
        const newDirty = new Set(prev);
        newDirty.delete(key);
        return newDirty;
      });
      setFieldErrors((prev) => {
        const { [key]: removed, ...rest } = prev;
        return rest;
      });
    },
    [initialData]
  );

  const getFieldError = useCallback(
    (key: string) => {
      return fieldErrors[key] || null;
    },
    [fieldErrors]
  );

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  const contextValue = useMemo(
    () => ({
      formData,
      updateFormData,
      batchUpdateFormData,
      getFormValue,
      validateField,
      isFieldDirty,
      resetField,
      getFieldError,
    }),
    [
      formData,
      updateFormData,
      batchUpdateFormData,
      getFormValue,
      validateField,
      isFieldDirty,
      resetField,
      getFieldError,
    ]
  );

  return (
    <TableFormContext.Provider value={contextValue}>
      {children}
    </TableFormContext.Provider>
  );
};

// Row action configuration
export interface RowAction<T> {
  id: string;
  label: string;
  icon?: string;
  onClick: (row: T, rowIndex: number) => void;
  disabled?: (row: T) => boolean;
  hidden?: (row: T) => boolean;
  variant?: 'primary' | 'secondary' | 'danger' | 'success';
}

// Bulk action configuration
export interface BulkAction<T> {
  id: string;
  label: string;
  icon?: string;
  onClick: (selectedRows: T[], selectedIndices: number[]) => void;
  disabled?: (selectedRows: T[]) => boolean;
  variant?: 'primary' | 'secondary' | 'danger' | 'success';
}

// Export configuration
export interface ExportConfig {
  enabled: boolean;
  formats?: ('csv' | 'excel' | 'pdf')[];
  filename?: string;
  customExporter?: (data: any[], format: string) => void;
}

// Virtual scrolling configuration
export interface VirtualScrollConfig {
  enabled: boolean;
  itemHeight?: number;
  overscan?: number;
  threshold?: number; // Minimum rows to enable virtualization
}

// Loading state configuration
export interface LoadingConfig {
  enabled: boolean;
  skeleton?: boolean;
  rows?: number;
  message?: string;
}

// Error handling configuration
export interface ErrorConfig {
  boundary?: boolean;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error) => void;
}

// CRUD operations configuration
export interface CrudConfig<T> {
  enabled: boolean;
  create?: {
    enabled: boolean;
    onAdd?: (newRow: Partial<T>) => Promise<T> | T;
    defaultValues?: Partial<T>;
    validation?: (row: Partial<T>) => Record<string, string> | null;
  };
  update?: {
    enabled: boolean;
    onUpdate?: (rowId: string | number, updates: Partial<T>) => Promise<T> | T;
    optimistic?: boolean; // Update UI immediately before server response
    validation?: (row: T, updates: Partial<T>) => Record<string, string> | null;
  };
  delete?: {
    enabled: boolean;
    onDelete?: (rowId: string | number) => Promise<void> | void;
    confirmation?: boolean | ((row: T) => string);
    softDelete?: boolean; // Mark as deleted instead of removing
  };
  bulkOperations?: {
    enabled: boolean;
    onBulkUpdate?: (
      rowIds: (string | number)[],
      updates: Partial<T>
    ) => Promise<T[]> | T[];
    onBulkDelete?: (rowIds: (string | number)[]) => Promise<void> | void;
  };
}

// Enhanced column visibility configuration
export interface ColumnVisibilityConfig<T> {
  // Basic visibility control
  visibleColumns?: (keyof T)[];
  hiddenColumns?: (keyof T)[];

  // Advanced visibility options
  maintainDataAccess?: boolean; // Keep hidden column data in form state
  dynamicVisibility?: (data: T[]) => (keyof T)[]; // Dynamic column visibility
  groupVisibility?: Record<string, (keyof T)[]>; // Group columns for bulk control

  // Conditional visibility per row
  conditionalVisibility?: Record<
    keyof T,
    (row: T, rowIndex: number) => boolean
  >;

  // Responsive visibility
  responsiveVisibility?: {
    mobile?: (keyof T)[];
    tablet?: (keyof T)[];
    desktop?: (keyof T)[];
  };
}

// Enhanced props interface for the dynamic table
export interface DynamicTableProps<
  T extends Record<string, any>,
  U extends Partial<keyof T>
> {
  // Core data and configuration
  data: T[];
  displayColumns?: U[];
  columnConfig?: Partial<Record<keyof T, ColumnConfig<T>>>;

  // Enhanced column visibility
  columnVisibilityConfig?: ColumnVisibilityConfig<T>;

  // Feature toggles
  enableSorting?: boolean;
  enableFiltering?: boolean;
  enablePagination?: boolean;
  enableInfoText?: boolean;
  enableRowSelection?: boolean;
  enableColumnResizing?: boolean;
  enableColumnReordering?: boolean;
  enableVirtualScrolling?: boolean;

  // CRUD operations
  crudConfig?: CrudConfig<T>;

  // Pagination and display
  pageSize?: number;
  pageSizeOptions?: number[];

  // Styling and layout
  className?: string;
  tableBorder?: string;
  height?: string | number;
  maxHeight?: string | number;

  // Actions and interactions
  rowActions?: RowAction<T>[];
  bulkActions?: BulkAction<T>[];
  onRowClick?: (row: T, rowIndex: number) => void;
  onRowDoubleClick?: (row: T, rowIndex: number) => void;
  onSelectionChange?: (selectedRows: T[], selectedIndices: number[]) => void;

  // Data management
  onDataChange?: (data: T[]) => void;
  onCellEdit?: (
    rowIndex: number,
    columnKey: keyof T,
    newValue: any,
    oldValue: any
  ) => void;

  // Configuration objects
  virtualScrollConfig?: VirtualScrollConfig;
  exportConfig?: ExportConfig;
  loadingConfig?: LoadingConfig;
  errorConfig?: ErrorConfig;

  // Server-side operations (for future enhancement)
  serverSide?: boolean;
  onServerSideFilter?: (filters: any) => Promise<T[]>;
  onServerSideSort?: (sorting: any) => Promise<T[]>;
  onServerSidePaginate?: (
    pagination: any
  ) => Promise<{ data: T[]; totalCount: number }>;

  // Accessibility
  ariaLabel?: string;
  ariaDescription?: string;

  // Advanced features
  groupBy?: keyof T;
  expandableRows?: boolean;
  renderExpandedRow?: (row: T, rowIndex: number) => React.ReactNode;

  // Performance optimization
  memoizeRows?: boolean;
  debounceMs?: number;

  // Form integration
  formIntegration?: {
    enabled: boolean;
    sharedFormState?: boolean;
    onFormSubmit?: (data: Record<string, any>) => void;
    onFormValidation?: (errors: Record<string, string>) => void;
  };
  // Client actions
  _callClientAction?: (config: ActionConfig[]) => void;
}

// Styled Components
const TableWrapper = styled.div<{ border?: string }>`
  overflow-x: auto;
  border-radius: 8px;
  ${({ border }) =>
    border
      ? css`
          border: ${border};
        `
      : ''}
`;

const StyledTable = styled.table`
  min-width: 100%;
  border-collapse: collapse;
`;

const Thead = styled.thead`
  /* background: #f9fafb; */
`;

const Th = styled.th`
  padding: 12px 24px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  /* color: #6b7280; */
  /* text-transform: uppercase; */
  letter-spacing: 0.05em;
  /* background: #f9fafb; */
  position: relative;
`;

const FilterInput = styled.input`
  margin-top: 4px;
  width: 100%;
  padding: 4px 8px;
  font-size: 12px;
  /* border: 1px solid #d1d5db; */
  border-radius: 4px;
  box-sizing: border-box;
`;

const Tbody = styled.tbody`
  /* background: #fff; */
`;

const Tr = styled.tr<{ clickable?: boolean; selected?: boolean }>`
  ${({ clickable }) => clickable && `cursor: pointer;`}
  /* ${({ selected }) => selected && `background: #eff6ff;`} */

  &:hover {
    /* background: ${({ selected }) => (selected ? '#dbeafe' : '#f3f4f6')}; */
  }
`;

const Td = styled.td`
  padding: 16px 24px;
  white-space: nowrap;
  font-size: 14px;
  font-style: '';
  /* color: #111827; */
`;

const PaginationWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
`;

const PaginationButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PaginationButton = styled.button`
  padding: 4px 12px;
  font-size: 14px;
  /* border: 1px solid #d1d5db; */
  border-radius: 4px;
  /* background: #fff; */
  cursor: pointer;
  transition: background 0.2s;
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const PageInfo = styled.span`
  font-size: 14px;
  /* color: #374151; */
`;

const PageSizeSelect = styled.select`
  padding: 4px 8px;
  font-size: 14px;
  /* border: 1px solid #d1d5db; */
  border-radius: 4px;
  /* background: #fff; */
`;

const GlobalFilterInput = styled.input`
  padding: 8px 12px;
  /* border: 1px solid #d1d5db; */
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 8px;
  width: 240px;
  &:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 2px #bfdbfe;
  }
`;

const InfoText = styled.div`
  font-size: 13px;
  /* color: #6b7280; */
  margin-top: 8px;
  text-align: left;
`;

const NoData = styled.div`
  text-align: center;
  padding: 32px 0;
  /* color: #9ca3af; */
  font-size: 16px;
`;

// Enhanced styled components for new features
const TableContainer = styled.div<{
  height?: string | number;
  maxHeight?: string | number;
}>`
  width: 100%;
  ${({ height }) =>
    height && `height: ${typeof height === 'number' ? `${height}px` : height};`}
  ${({ maxHeight }) =>
    maxHeight &&
    `max-height: ${
      typeof maxHeight === 'number' ? `${maxHeight}px` : maxHeight
    };`}
  overflow: auto;
`;

const FilterContainer = styled.div`
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const BulkActionsContainer = styled.div`
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
`;

const BulkActionButton = styled.button<{ variant?: string }>`
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;

  ${({ variant }) => {
    switch (variant) {
      case 'danger':
        return `
          background: #dc2626;
          color: white;
          &:hover { background: #b91c1c; }
        `;
      case 'success':
        return `
          background: #16a34a;
          color: white;
          &:hover { background: #15803d; }
        `;
      case 'secondary':
        return `
          background: #6b7280;
          color: white;
          &:hover { background: #4b5563; }
        `;
      default:
        return `
          background: #2563eb;
          color: white;
          &:hover { background: #1d4ed8; }
        `;
    }
  }}

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
`;

const HeaderText = styled.span<{ sortable?: boolean }>`
  cursor: ${({ sortable }) => (sortable ? 'pointer' : 'default')};
  user-select: ${({ sortable }) => (sortable ? 'none' : 'auto')};
  flex: 1;
`;

const SortIndicator = styled.span`
  color: #9ca3af;
  font-size: 12px;
`;

const ResizeHandle = styled.div`
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: transparent;
  cursor: col-resize;
  user-select: none;
  touch-action: none;

  &:hover {
    background: #2563eb;
  }
`;

const RowActionsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

const RowActionButton = styled.button<{ variant?: string }>`
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;

  ${({ variant }) => {
    switch (variant) {
      case 'danger':
        return `
          background: #fef2f2;
          color: #dc2626;
          border-color: #fecaca;
          &:hover { background: #fee2e2; }
        `;
      case 'success':
        return `
          background: #f0fdf4;
          color: #16a34a;
          border-color: #bbf7d0;
          &:hover { background: #dcfce7; }
        `;
      case 'secondary':
        return `
          background: #f8fafc;
          color: #6b7280;
          border-color: #e2e8f0;
          &:hover { background: #f1f5f9; }
        `;
      default:
        return `
          background: #eff6ff;
          color: #2563eb;
          border-color: #bfdbfe;
          &:hover { background: #dbeafe; }
        `;
    }
  }}

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const PaginationInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 64px 32px;
  color: #6b7280;
`;

const SkeletonRow = styled.div`
  height: 48px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  margin-bottom: 8px;
  border-radius: 4px;

  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
`;

const SkeletonLoader: React.FC<{ rows?: number }> = ({ rows = 5 }) => (
  <div>
    {Array.from({ length: rows }, (_, i) => (
      <SkeletonRow key={i} />
    ))}
  </div>
);

// Column visibility and export styled components
const ColumnVisibilityContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const ColumnVisibilityButton = styled.button`
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background: #f9fafb;
  }
`;

const ColumnVisibilityDropdown = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 200px;
  max-height: 300px;
  overflow-y: auto;
  display: none;
`;

const ColumnVisibilityItem = styled.div`
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background: #f9fafb;
  }

  input {
    margin: 0;
  }

  label {
    margin: 0;
    cursor: pointer;
    font-size: 14px;
  }
`;

const ExportContainer = styled.div`
  display: flex;
  gap: 8px;
`;

const ExportButton = styled.button`
  padding: 8px 16px;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    background: #1d4ed8;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

// Virtual scrolling components
const VirtualScrollContainer = styled.div<{ height: number }>`
  height: ${({ height }) => height}px;
  overflow: auto;
  position: relative;
`;

const VirtualScrollContent = styled.div<{ totalHeight: number }>`
  height: ${({ totalHeight }) => totalHeight}px;
  position: relative;
`;

const VirtualScrollViewport = styled.div<{
  translateY: number;
  height: number;
}>`
  transform: translateY(${({ translateY }) => translateY}px);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: ${({ height }) => height}px;
`;

// Enhanced column visibility hook with data access management
const useColumnVisibility = <T extends Record<string, any>>(
  data: T[],
  columnConfig: Partial<Record<keyof T, ColumnConfig<T>>>,
  columnVisibilityConfig?: ColumnVisibilityConfig<T>,
  displayColumns?: Partial<keyof T>[]
) => {
  // Initialize column visibility state
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    () => {
      const initialVisibility: VisibilityState = {};

      // Handle legacy displayColumns prop
      if (displayColumns) {
        for (const key in columnConfig) {
          initialVisibility[key] = displayColumns.includes(
            key as Partial<keyof T>
          );
        }
        return initialVisibility;
      }

      // Handle new columnVisibilityConfig
      if (columnVisibilityConfig) {
        // Start with all columns visible by default
        if (data.length > 0) {
          Object.keys(data[0]).forEach((key) => {
            initialVisibility[key] = true;
          });
        }

        // Apply visible columns if specified
        if (columnVisibilityConfig.visibleColumns) {
          Object.keys(initialVisibility).forEach((key) => {
            initialVisibility[key] =
              columnVisibilityConfig.visibleColumns?.includes(key as keyof T) ??
              false;
          });
        }

        // Apply hidden columns if specified
        if (columnVisibilityConfig.hiddenColumns) {
          columnVisibilityConfig.hiddenColumns.forEach((key) => {
            initialVisibility[key as string] = false;
          });
        }

        // Apply dynamic visibility if specified
        if (columnVisibilityConfig.dynamicVisibility) {
          const dynamicVisible = columnVisibilityConfig.dynamicVisibility(data);
          Object.keys(initialVisibility).forEach((key) => {
            if (!dynamicVisible.includes(key as keyof T)) {
              initialVisibility[key] = false;
            }
          });
        }
      }

      return initialVisibility;
    }
  );

  // Track hidden columns that should maintain data access
  const hiddenColumnsWithDataAccess = useMemo(() => {
    const hiddenWithAccess: string[] = [];

    if (columnVisibilityConfig?.maintainDataAccess) {
      Object.entries(columnVisibility).forEach(([key, visible]) => {
        if (!visible) {
          const config = columnConfig[key as keyof T];
          if (
            config?.controlType ||
            config?.visibilityConfig?.maintainDataAccess !== false
          ) {
            hiddenWithAccess.push(key);
          }
        }
      });
    }

    return hiddenWithAccess;
  }, [columnVisibility, columnVisibilityConfig, columnConfig]);

  // Function to toggle column visibility
  const toggleColumnVisibility = useCallback(
    (columnId: string, visible?: boolean) => {
      setColumnVisibility((prev) => ({
        ...prev,
        [columnId]: visible !== undefined ? visible : !prev[columnId],
      }));
    },
    []
  );

  // Function to toggle group visibility
  const toggleGroupVisibility = useCallback(
    (groupName: string, visible: boolean) => {
      if (columnVisibilityConfig?.groupVisibility?.[groupName]) {
        const updates: Record<string, boolean> = {};
        columnVisibilityConfig.groupVisibility[groupName].forEach(
          (columnKey) => {
            updates[columnKey as string] = visible;
          }
        );

        setColumnVisibility((prev) => ({ ...prev, ...updates }));
      }
    },
    [columnVisibilityConfig]
  );

  return {
    columnVisibility,
    setColumnVisibility,
    hiddenColumnsWithDataAccess,
    toggleColumnVisibility,
    toggleGroupVisibility,
  };
};

// Enhanced virtual scrolling hook with performance optimizations
const useVirtualScrolling = <T,>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5,
}: {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollTopRef = useRef(0);
  const rafRef = useRef<number>();

  // Optimized scroll handler with RAF throttling
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    scrollTopRef.current = newScrollTop;

    // Cancel previous RAF if still pending
    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
    }

    // Use RAF to throttle scroll updates
    rafRef.current = requestAnimationFrame(() => {
      setScrollTop(scrollTopRef.current);
    });
  }, []);

  // Cleanup RAF on unmount
  useEffect(() => {
    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, []);

  // Memoized calculations for better performance
  const calculations = useMemo(() => {
    const totalHeight = items.length * itemHeight;
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const startIndex = Math.max(
      0,
      Math.floor(scrollTop / itemHeight) - overscan
    );
    const endIndex = Math.min(
      items.length - 1,
      startIndex + visibleCount + overscan * 2
    );
    const offsetY = startIndex * itemHeight;

    return {
      totalHeight,
      visibleCount,
      startIndex,
      endIndex,
      offsetY,
    };
  }, [items.length, itemHeight, containerHeight, scrollTop, overscan]);

  // Memoized visible items slice
  const visibleItems = useMemo(() => {
    return items.slice(calculations.startIndex, calculations.endIndex + 1);
  }, [items, calculations.startIndex, calculations.endIndex]);

  return {
    ...calculations,
    visibleItems,
    handleScroll,
  };
};

// Performance monitoring hooks
const useRenderCount = (componentName: string) => {
  const renderCount = useRef(0);
  renderCount.current += 1;

  if (process.env.NODE_ENV === 'development') {
    console.log(`${componentName} rendered ${renderCount.current} times`);
  }

  return renderCount.current;
};

const usePerformanceMetrics = (enabled = false) => {
  const metricsRef = useRef({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0,
    totalRenderTime: 0,
  });

  const startTime = useRef(0);

  useEffect(() => {
    if (!enabled) return;

    startTime.current = performance.now();
    metricsRef.current.renderCount += 1;
  });

  useEffect(() => {
    if (!enabled) return;

    const endTime = performance.now();
    const renderTime = endTime - startTime.current;

    metricsRef.current.lastRenderTime = renderTime;
    metricsRef.current.totalRenderTime += renderTime;
    metricsRef.current.averageRenderTime =
      metricsRef.current.totalRenderTime / metricsRef.current.renderCount;

    if (process.env.NODE_ENV === 'development') {
      console.log('Render metrics:', {
        renderCount: metricsRef.current.renderCount,
        lastRenderTime: renderTime.toFixed(2) + 'ms',
        averageRenderTime:
          metricsRef.current.averageRenderTime.toFixed(2) + 'ms',
      });
    }
  });

  return metricsRef.current;
};

const useWhyDidYouUpdate = (name: string, props: Record<string, any>) => {
  const previousProps = useRef<Record<string, any>>();

  useEffect(() => {
    if (previousProps.current && process.env.NODE_ENV === 'development') {
      const allKeys = Object.keys({ ...previousProps.current, ...props });
      const changedProps: Record<string, { from: any; to: any }> = {};

      allKeys.forEach((key) => {
        if (previousProps.current![key] !== props[key]) {
          changedProps[key] = {
            from: previousProps.current![key],
            to: props[key],
          };
        }
      });

      if (Object.keys(changedProps).length) {
        console.log('[why-did-you-update]', name, changedProps);
      }
    }

    previousProps.current = props;
  });
};

// Highly optimized checkbox component with minimal re-renders
const OptimizedCheckbox = memo(
  ({
    checked,
    onChange,
    name,
    hideLabel,
    disabled,
    rowIndex,
  }: {
    checked: boolean;
    onChange: (checked: boolean) => void;
    name: string;
    hideLabel?: boolean;
    disabled?: boolean;
    rowIndex?: number;
  }) => {
    // Use a stable callback to prevent parent re-renders
    const handleChange = useCallback(
      (newChecked: boolean) => {
        onChange(newChecked);
      },
      [onChange]
    );

    return (
      <SelectionBox
        name={name}
        checked={checked}
        onChange={handleChange}
        hideLabel={hideLabel}
        disabled={disabled}
        aria-label={`${name} for row ${
          rowIndex !== undefined ? rowIndex + 1 : ''
        }`}
      />
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if essential props changed
    return (
      prevProps.checked === nextProps.checked &&
      prevProps.disabled === nextProps.disabled &&
      prevProps.name === nextProps.name &&
      prevProps.hideLabel === nextProps.hideLabel
    );
  }
);

// Highly optimized form control wrapper with minimal FormBuilder usage
const OptimizedFormControl = memo(
  <T extends Record<string, any>>({
    config,
    currentValue,
    row,
    rowIndex,
    uniqueControlName,
    _callClientAction,
  }: {
    config: ColumnConfig<T>;
    currentValue: any;
    row: T;
    rowIndex: number;
    uniqueControlName: string;
    _callClientAction?: (config: ActionConfig[]) => void;
  }) => {
    // Stable change handler to prevent parent re-renders
    const handleChange = useCallback(
      (newValue: any) => {
        if (config.onControlChange) {
          if (typeof config.onControlChange === 'function') {
            config.onControlChange(newValue, row, rowIndex);
          } else if (
            _callClientAction &&
            Array.isArray(config.onControlChange)
          ) {
            _callClientAction(
              config.onControlChange.map((action) => ({
                ...action,
                param: { value: newValue, row, rowIndex },
              }))
            );
          }
        }
      },
      [config.onControlChange, row, rowIndex, _callClientAction]
    );

    // Memoized control configuration to prevent recreation on every render
    const controlConfig = useMemo((): InputControlConfig => {
      const baseConfig = {
        name: config?.customControlName || uniqueControlName,
        type: config.controlType as any,
        ...(config.controlConfig || {}),
        ...(config.controlProps || {}),
        value:
          config.controlType === 'checkbox' ? !!currentValue : currentValue,
      };

      // Enhanced configurations for specific control types
      switch (config.controlType) {
        case 'single-select':
          return {
            ...baseConfig,
            type: 'single-select',
            options: config.controlProps?.options || {
              source: 'literal',
              data: [],
            },
            labelProp: config.controlProps?.labelProp || 'label',
            valueProp: config.controlProps?.valueProp || 'value',
          } as any;

        case 'multi-select':
          return {
            ...baseConfig,
            type: 'multi-select',
            options: config.controlProps?.options || {
              source: 'literal',
              data: [],
            },
            labelProp: config.controlProps?.labelProp || 'label',
            valueProp: config.controlProps?.valueProp || 'value',
          } as any;

        case 'radio-group':
          return {
            ...baseConfig,
            type: 'radio-group',
            options: config.controlProps?.options || {
              source: 'literal',
              data: [],
            },
            returnBoolean: config.controlProps?.returnBoolean || false,
            size: config.controlProps?.size || 'medium',
          } as any;

        case 'datepicker':
          return {
            ...baseConfig,
            type: 'datepicker',
            placeholder: config.controlProps?.placeholder || 'Select date...',
          } as any;

        case 'date-and-time-picker':
          return {
            ...baseConfig,
            type: 'date-and-time-picker',
            placeholder:
              config.controlProps?.placeholder || 'Select date and time...',
          } as any;

        case 'timepicker':
          return {
            ...baseConfig,
            type: 'timepicker',
            placeholder: config.controlProps?.placeholder || 'Select time...',
            validMinutes: config.controlProps?.validMinutes,
          } as any;

        case 'textarea':
          return {
            ...baseConfig,
            type: 'textarea',
            rows: config.controlProps?.rows || 3,
            cols: config.controlProps?.cols,
            maxLength: config.controlProps?.maxLength,
            placeholder: config.controlProps?.placeholder || '',
          } as any;

        case 'add-file':
          return {
            ...baseConfig,
            type: 'add-file',
            placeholder: config.controlProps?.placeholder || 'Choose file...',
          } as any;

        default:
          return baseConfig as InputControlConfig;
      }
    }, [config, uniqueControlName, currentValue]);

    // Memoized form configuration with optimized change handling
    const formConfig = useMemo(
      () => ({
        controls: [controlConfig],
        style: { margin: 0, padding: 0 },
        onSubmit: (formData: any) => {
          const newValue = formData[uniqueControlName];
          handleChange(newValue);
        },
        onChange: (formData: any) => {
          const newValue = formData[uniqueControlName];
          handleChange(newValue);
        },
      }),
      [controlConfig, uniqueControlName, handleChange]
    );

    // Memoized default values
    const defaultValues = useMemo(
      () => ({
        [uniqueControlName]: currentValue,
      }),
      [uniqueControlName, currentValue]
    );

    // Memoized validation
    const validationErrors = useMemo(
      () => (config.validator ? config.validator(currentValue) : null),
      [config.validator, currentValue]
    );

    return (
      <div style={{ position: 'relative' }}>
        <Suspense fallback={<div>Loading...</div>}>
          <FormBuilder
            config={formConfig}
            defaultValues={defaultValues}
            isStory={true}
            key={`${uniqueControlName}-${currentValue}`}
          />
        </Suspense>
        {validationErrors && (
          <div
            style={{
              position: 'absolute',
              top: '100%',
              left: 0,
              background: '#fee2e2',
              color: '#dc2626',
              padding: '2px 6px',
              fontSize: '12px',
              borderRadius: '3px',
              zIndex: 10,
              whiteSpace: 'nowrap',
            }}
          >
            {validationErrors}
          </div>
        )}
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Optimized comparison for form controls
    return (
      prevProps.currentValue === nextProps.currentValue &&
      prevProps.rowIndex === nextProps.rowIndex &&
      prevProps.config.controlType === nextProps.config.controlType &&
      prevProps.uniqueControlName === nextProps.uniqueControlName
    );
  }
);

// Memoized cell renderer for performance optimization
const MemoizedCellRenderer = memo(
  <T extends Record<string, any>>({
    value,
    config,
    row,
    rowIndex,
    _callClientAction,
  }: {
    value: any;
    config?: ColumnConfig<T>;
    row: T;
    rowIndex: number;
    _callClientAction?: (config: ActionConfig[]) => void;
  }) => {
    // If a cell render function is provided, use it
    if (config?.cell) {
      return config.cell(
        value,
        row,
        rowIndex,
        config.onControlChange
          ? (val: any) =>
              typeof config.onControlChange === 'function'
                ? config.onControlChange(val, row, rowIndex)
                : _callClientAction &&
                  Array.isArray(config.onControlChange) &&
                  config.onControlChange.length > 0 &&
                  (config.onControlChange satisfies ActionConfig[])
                ? _callClientAction(
                    (config.onControlChange as ActionConfig[]).map((o) => ({
                      ...o,
                      param: { value: val, row, rowIndex },
                    }))
                  )
                : undefined
          : undefined
      );
    }

    // Render form controls if specified
    if (config?.controlType && row) {
      const controlName = config.key as keyof T as string;
      const currentValue = row[config.key];

      // For simple checkbox, use optimized component to avoid FormBuilder overhead
      if (config.controlType === 'checkbox') {
        const headerText =
          typeof config.header === 'string'
            ? config.header
            : typeof config.header === 'object' && config.header?.text
            ? config.header.text
            : controlName;

        // Stable change handler for checkbox
        const handleCheckboxChange = useCallback(
          (checked: boolean) => {
            if (config.onControlChange) {
              if (typeof config.onControlChange === 'function') {
                config.onControlChange(checked, row, rowIndex);
              } else if (
                _callClientAction &&
                Array.isArray(config.onControlChange)
              ) {
                _callClientAction(
                  config.onControlChange.map((action) => ({
                    ...action,
                    param: { value: checked, row, rowIndex },
                  }))
                );
              }
            }
          },
          [config.onControlChange, row, rowIndex, _callClientAction]
        );

        return (
          <OptimizedCheckbox
            checked={!!currentValue}
            onChange={handleCheckboxChange}
            name={headerText}
            hideLabel={config.controlProps?.hideLabel}
            disabled={config.controlProps?.disabled}
            rowIndex={rowIndex}
          />
        );
      }

      // For other controls, use optimized form control wrapper
      const uniqueControlName = `${controlName}_row_${rowIndex}`;

      return (
        <OptimizedFormControl
          config={config as ColumnConfig<Record<string, any>>}
          currentValue={currentValue}
          row={row}
          rowIndex={rowIndex}
          uniqueControlName={uniqueControlName}
          _callClientAction={_callClientAction}
        />
      );
    }

    // If a bodyTextConfig is provided, use it for Text rendering
    if (config?.bodyTextConfig) {
      const textConfig = config.bodyTextConfig(value, row);
      return <Text textItems={[textConfig]} />;
    }

    // Fallbacks with enhanced type handling
    if (value === null || value === undefined) {
      return (
        <Text
          textItems={[
            {
              text: '-',
              options: {
                format: 'paragraph',
                type: 'value',
              },
            },
          ]}
        />
      );
    }

    if (typeof value === 'boolean') {
      return (
        <Text
          textItems={[
            {
              text: value ? 'Yes' : 'No',
              options: {
                format: 'paragraph',
                type: 'value',
                style: {
                  padding: '2px 8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: 500,
                  background: value ? '#dcfce7' : '#fee2e2',
                  color: value ? '#166534' : '#991b1b',
                },
              },
            },
          ]}
        />
      );
    }

    if (typeof value === 'number') {
      const formattedValue = config?.formatter
        ? config.formatter(value)
        : value.toLocaleString();
      return (
        <Text
          textItems={[
            {
              text: formattedValue,
              options: {
                format: 'paragraph',
                type: 'value',
                style: { fontFamily: 'monospace' },
              },
            },
          ]}
        />
      );
    }

    if (value instanceof Date) {
      const formattedValue = config?.formatter
        ? config.formatter(value)
        : value.toLocaleDateString();
      return (
        <Text
          textItems={[
            {
              text: formattedValue,
              options: {
                format: 'paragraph',
                type: 'value',
                style: { fontFamily: 'monospace' },
              },
            },
          ]}
        />
      );
    }

    if (typeof value === 'string' && value.startsWith('http')) {
      return (
        <Text
          textItems={[
            {
              text: 'Link',
              options: {
                format: 'paragraph',
                type: 'value',
                style: { color: '#2563eb', textDecoration: 'underline' },
              },
            },
          ]}
        />
      );
    }

    if (typeof value === 'object') {
      const displayValue = config?.formatter
        ? config.formatter(value)
        : JSON.stringify(value, null, 2);
      return (
        <Text
          textItems={[
            {
              text: displayValue,
              options: {
                format: 'paragraph',
                type: 'value',
                style: {
                  fontSize: '12px',
                  background: '#f3f4f6',
                  padding: '4px',
                  borderRadius: '4px',
                  maxWidth: '320px',
                  overflow: 'hidden',
                },
              },
            },
          ]}
        />
      );
    }

    const displayValue = config?.formatter
      ? config.formatter(value)
      : String(value);
    return (
      <Text
        textItems={[
          {
            text: displayValue,
            options: {
              format: 'paragraph',
              type: 'value',
            },
          },
        ]}
      />
    );
  },
  (prevProps, nextProps) => {
    // Fast path: if row index changed, always re-render
    if (prevProps.rowIndex !== nextProps.rowIndex) return false;

    // Fast path: if value changed, re-render
    if (prevProps.value !== nextProps.value) return false;

    // Fast path: if config changed, re-render
    if (prevProps.config !== nextProps.config) return false;

    // For control types, check the actual row value for the specific key
    if (prevProps.config?.controlType && nextProps.config?.controlType) {
      const prevRowValue = prevProps.row[prevProps.config.key];
      const nextRowValue = nextProps.row[nextProps.config.key];

      // Only re-render if the specific field value changed
      if (prevRowValue !== nextRowValue) return false;

      // Also check if control type changed
      if (prevProps.config.controlType !== nextProps.config.controlType)
        return false;
    }

    // For cells with custom render functions, do a shallow comparison
    if (prevProps.config?.cell && nextProps.config?.cell) {
      // Re-render if the cell function changed or row data changed
      if (prevProps.config.cell !== nextProps.config.cell) return false;
      if (prevProps.row !== nextProps.row) return false;
    }

    // For text config cells, check if the config changed
    if (prevProps.config?.bodyTextConfig && nextProps.config?.bodyTextConfig) {
      if (prevProps.config.bodyTextConfig !== nextProps.config.bodyTextConfig)
        return false;
    }

    // Default: don't re-render if nothing significant changed
    return true;
  }
);

// Helper function to render cell content
const renderCell = <T extends Record<string, any>>(
  value: any,
  config: ColumnConfig<T> | undefined,
  row: T,
  rowIndex: number,
  _callClientAction?: (config: ActionConfig[]) => void
): React.ReactNode => {
  return (
    <MemoizedCellRenderer
      value={value}
      config={config as ColumnConfig<Record<string, any>>}
      row={row}
      rowIndex={rowIndex}
      _callClientAction={_callClientAction}
    />
  );
};

// Memoized row component to prevent unnecessary row re-renders
const MemoizedTableRow = memo(
  <T extends Record<string, any>>({
    row,
    rowIndex,
    columnConfig,
    columns,
    rowActions,
    enableRowSelection,
    focusedCell,
    setFocusedCell,
    onRowClick,
    onRowDoubleClick,
    itemHeight,
  }: {
    row: any; // TanStack Table Row type
    rowIndex: number;
    columnConfig: Partial<Record<keyof T, ColumnConfig<T>>>;
    columns: any[]; // TanStack Table Column type
    rowActions: any[];
    enableRowSelection: boolean;
    focusedCell: { rowIndex: number; columnIndex: number } | null;
    setFocusedCell: (cell: { rowIndex: number; columnIndex: number }) => void;
    onRowClick?: (row: T, rowIndex: number) => void;
    onRowDoubleClick?: (row: T, rowIndex: number) => void;
    itemHeight?: number;
  }) => {
    return (
      <Tr
        key={row.id}
        onClick={
          onRowClick ? () => onRowClick(row.original, rowIndex) : undefined
        }
        onDoubleClick={
          onRowDoubleClick
            ? () => onRowDoubleClick(row.original, rowIndex)
            : undefined
        }
        clickable={!!onRowClick || !!onRowDoubleClick}
        selected={enableRowSelection && row.getIsSelected()}
        style={itemHeight ? { height: itemHeight } : undefined}
      >
        {row.getVisibleCells().map((cell: any, cellIndex: number) => (
          <Td
            key={cell.id}
            tabIndex={
              focusedCell?.rowIndex === rowIndex &&
              focusedCell?.columnIndex === cellIndex
                ? 0
                : -1
            }
            role="gridcell"
            aria-selected={enableRowSelection ? row.getIsSelected() : undefined}
            onClick={() =>
              setFocusedCell({
                rowIndex: rowIndex,
                columnIndex: cellIndex,
              })
            }
          >
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </Td>
        ))}
        {rowActions.length > 0 && (
          <Td>
            <RowActionsContainer>
              {rowActions.map(
                (action) =>
                  !action.hidden?.(row.original) && (
                    <RowActionButton
                      key={action.id}
                      variant={action.variant}
                      onClick={() => action.onClick(row.original, rowIndex)}
                      disabled={action.disabled?.(row.original)}
                      title={action.label}
                    >
                      {action.label}
                    </RowActionButton>
                  )
              )}
            </RowActionsContainer>
          </Td>
        )}
      </Tr>
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if essential row properties changed
    if (prevProps.rowIndex !== nextProps.rowIndex) return false;
    if (prevProps.row.id !== nextProps.row.id) return false;

    // Check if row selection state changed
    if (prevProps.enableRowSelection && nextProps.enableRowSelection) {
      const prevSelected = prevProps.row.getIsSelected();
      const nextSelected = nextProps.row.getIsSelected();
      if (prevSelected !== nextSelected) return false;
    }

    // Check if focused cell changed for this row
    const prevFocused = prevProps.focusedCell?.rowIndex === prevProps.rowIndex;
    const nextFocused = nextProps.focusedCell?.rowIndex === nextProps.rowIndex;
    if (prevFocused !== nextFocused) return false;

    // Check if the actual row data changed (shallow comparison)
    const prevRowData = prevProps.row.original;
    const nextRowData = nextProps.row.original;
    if (prevRowData !== nextRowData) {
      // Do a deeper check - only re-render if values actually changed
      const keys = Object.keys(prevRowData);
      for (const key of keys) {
        if (prevRowData[key] !== nextRowData[key]) {
          return false;
        }
      }
    }

    // Check if column configuration changed
    if (prevProps.columnConfig !== nextProps.columnConfig) return false;

    // Check if row actions changed
    if (prevProps.rowActions !== nextProps.rowActions) return false;

    // Don't re-render if nothing significant changed
    return true;
  }
);

// Helper function to render header with Text if config provided
const renderHeader = (header: string | TextConfig | undefined) => {
  if (!header) return null;
  if (typeof header === 'string') {
    return (
      <Text
        textItems={[
          {
            text: header,
            options: { format: 'heading' },
          },
        ]}
      />
    );
  }
  return <Text textItems={[header]} />;
};

// Enhanced main dynamic table component with performance optimizations
export const DynamicTable = memo(
  <T extends Record<string, any>, U extends Partial<keyof T>>({
    data,
    columnConfig = {},
    enableSorting = false,
    enableFiltering = false,
    enablePagination = false,
    enableInfoText = false,
    enableRowSelection = false,
    enableColumnResizing = false,
    enableVirtualScrolling = false,
    pageSize = 10,
    pageSizeOptions = [10, 20, 30, 40, 50],
    className = '',
    tableBorder,
    height,
    maxHeight,
    rowActions = [],
    bulkActions = [],
    onRowClick,
    onRowDoubleClick,
    onSelectionChange,
    onDataChange,
    onCellEdit,
    virtualScrollConfig = {
      enabled: false,
      itemHeight: 50,
      overscan: 5,
      threshold: 100,
    },
    exportConfig = { enabled: false },
    loadingConfig = { enabled: false },
    errorConfig = { boundary: false },
    ariaLabel,
    ariaDescription,
    debounceMs = 300,
    memoizeRows = true,
    formIntegration = { enabled: false },
    _callClientAction,
    displayColumns,
    columnVisibilityConfig,
  }: DynamicTableProps<T, U>) => {
    // State management with performance optimizations
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [globalFilter, setGlobalFilter] = useState('');
    const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

    // Use the enhanced column visibility hook
    const {
      columnVisibility,
      setColumnVisibility,
      hiddenColumnsWithDataAccess,
      toggleColumnVisibility,
      toggleGroupVisibility,
    } = useColumnVisibility(
      data,
      columnConfig,
      columnVisibilityConfig,
      displayColumns
    );
    const [columnSizing, setColumnSizing] = useState<ColumnSizingState>({});
    const [pagination, setPagination] = useState<PaginationState>({
      pageIndex: 0,
      pageSize,
    });

    // Performance optimization refs
    const tableRef = useRef<HTMLDivElement>(null);
    const debouncedGlobalFilter = useRef(
      debounce((value: string) => setGlobalFilter(value), debounceMs)
    ).current;

    // Accessibility and keyboard navigation state
    const [focusedCell, setFocusedCell] = useState<{
      rowIndex: number;
      columnIndex: number;
    } | null>(null);
    const [announceText, setAnnounceText] = useState<string>('');

    // Lazy loading state (must be before early returns)
    const [isLazyLoading, setIsLazyLoading] = useState(false);
    const [lazyLoadedData, setLazyLoadedData] = useState<T[]>(data);

    // Virtual scrolling logic (must be before early returns)
    const shouldUseVirtualScrolling = useMemo(() => {
      return (
        enableVirtualScrolling &&
        virtualScrollConfig.enabled &&
        data.length >= (virtualScrollConfig.threshold || 100)
      );
    }, [enableVirtualScrolling, virtualScrollConfig, data.length]);

    const containerHeight = useMemo(() => {
      if (typeof height === 'number') return height;
      if (typeof height === 'string' && height.endsWith('px')) {
        return parseInt(height);
      }
      return 400; // Default height for virtual scrolling
    }, [height]);

    // Use lazy loaded data for table
    const tableData = useMemo(() => {
      return isLazyLoading ? lazyLoadedData : data;
    }, [isLazyLoading, lazyLoadedData, data]);

    // Lazy loading effect
    useEffect(() => {
      if (data.length > 1000) {
        setIsLazyLoading(true);
        // Simulate lazy loading by initially showing only first 100 items
        setLazyLoadedData(data.slice(0, 100));

        // Gradually load more data
        const timer = setTimeout(() => {
          setLazyLoadedData(data);
          setIsLazyLoading(false);
        }, 100);

        return () => clearTimeout(timer);
      } else {
        setLazyLoadedData(data);
        setIsLazyLoading(false);
      }
    }, [data]);

    // Debounced global filter handler
    const handleGlobalFilterChange = useCallback(
      (value: string) => {
        debouncedGlobalFilter(value);
      },
      [debouncedGlobalFilter]
    );

    // Optimized column generation with better memoization
    const columnStructure = useMemo(() => {
      if (!data || data.length === 0) return [];
      const sampleRow = data[0];
      return Object.keys(sampleRow) as (keyof T)[];
    }, [data?.length > 0 ? Object.keys(data[0]).join(',') : '']);

    const columns = useMemo<ColumnDef<T>[]>(() => {
      if (columnStructure.length === 0) return [];

      return columnStructure.map((key) => {
        const config = columnConfig[key];

        return {
          accessorKey: key,
          header: () =>
            config?.headerTextConfig ? (
              <Text textItems={[config.headerTextConfig]} />
            ) : (
              renderHeader(config?.header)
            ),
          cell: ({ getValue, row }) =>
            renderCell(
              getValue(),
              config,
              row.original,
              row.index,
              _callClientAction
            ),
          enableSorting: config?.sortable !== false && enableSorting,
          enableColumnFilter: config?.filterable !== false && enableFiltering,
          enableResizing: enableColumnResizing && config?.resizable !== false,
          size: config?.width
            ? typeof config.width === 'string'
              ? parseInt(config.width)
              : config.width
            : undefined,
          minSize: config?.minWidth,
          maxSize: config?.maxWidth,
          footer: config?.footerTextConfig
            ? () => <Text textItems={[config.footerTextConfig!]} />
            : undefined,
        };
      });
    }, [
      columnStructure,
      columnConfig,
      enableSorting,
      enableFiltering,
      enableColumnResizing,
      _callClientAction,
    ]);

    // Virtual scrolling implementation
    const virtualScrolling = useVirtualScrolling({
      items: tableData,
      itemHeight: virtualScrollConfig.itemHeight || 50,
      containerHeight: containerHeight - 100,
      overscan: virtualScrollConfig.overscan || 5,
    });

    // Enhanced table configuration with new features
    const table = useReactTable({
      data: tableData,
      columns,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getFilteredRowModel: getFilteredRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      state: {
        sorting,
        columnFilters,
        globalFilter,
        rowSelection: enableRowSelection ? rowSelection : {},
        columnVisibility,
        columnSizing: enableColumnResizing ? columnSizing : {},
        pagination,
      },
      onSortingChange: setSorting,
      onColumnFiltersChange: setColumnFilters,
      onGlobalFilterChange: setGlobalFilter,
      onRowSelectionChange: enableRowSelection ? setRowSelection : undefined,
      onColumnVisibilityChange: setColumnVisibility,
      onColumnSizingChange: enableColumnResizing ? setColumnSizing : undefined,
      onPaginationChange: setPagination,
      enableSorting,
      enableFilters: enableFiltering,
      enableRowSelection,
      enableColumnResizing,
      globalFilterFn: 'includesString',
    });

    // Keyboard navigation handler (after table definition)
    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent) => {
        if (!focusedCell) return;

        const { rowIndex, columnIndex } = focusedCell;
        const rows = table.getRowModel().rows;
        const columns = table.getVisibleLeafColumns();

        switch (e.key) {
          case 'ArrowUp':
            e.preventDefault();
            if (rowIndex > 0) {
              setFocusedCell({ rowIndex: rowIndex - 1, columnIndex });
              setAnnounceText(`Row ${rowIndex}, Column ${columnIndex + 1}`);
            }
            break;
          case 'ArrowDown':
            e.preventDefault();
            if (rowIndex < rows.length - 1) {
              setFocusedCell({ rowIndex: rowIndex + 1, columnIndex });
              setAnnounceText(`Row ${rowIndex + 2}, Column ${columnIndex + 1}`);
            }
            break;
          case 'ArrowLeft':
            e.preventDefault();
            if (columnIndex > 0) {
              setFocusedCell({ rowIndex, columnIndex: columnIndex - 1 });
              setAnnounceText(`Row ${rowIndex + 1}, Column ${columnIndex}`);
            }
            break;
          case 'ArrowRight':
            e.preventDefault();
            if (columnIndex < columns.length - 1) {
              setFocusedCell({ rowIndex, columnIndex: columnIndex + 1 });
              setAnnounceText(`Row ${rowIndex + 1}, Column ${columnIndex + 2}`);
            }
            break;
          case 'Home':
            e.preventDefault();
            if (e.ctrlKey) {
              setFocusedCell({ rowIndex: 0, columnIndex: 0 });
              setAnnounceText('First cell');
            } else {
              setFocusedCell({ rowIndex, columnIndex: 0 });
              setAnnounceText(`Row ${rowIndex + 1}, First column`);
            }
            break;
          case 'End':
            e.preventDefault();
            if (e.ctrlKey) {
              setFocusedCell({
                rowIndex: rows.length - 1,
                columnIndex: columns.length - 1,
              });
              setAnnounceText('Last cell');
            } else {
              setFocusedCell({ rowIndex, columnIndex: columns.length - 1 });
              setAnnounceText(`Row ${rowIndex + 1}, Last column`);
            }
            break;
          case 'Enter':
          case ' ':
            e.preventDefault();
            if (enableRowSelection) {
              const row = rows[rowIndex];
              row.toggleSelected();
              setAnnounceText(
                `Row ${rowIndex + 1} ${
                  row.getIsSelected() ? 'selected' : 'deselected'
                }`
              );
            }
            break;
        }
      },
      [focusedCell, table, enableRowSelection, setFocusedCell, setAnnounceText]
    );

    // Enhanced export functionality
    const exportData = useCallback(
      (format: 'csv' | 'excel' | 'pdf') => {
        if (exportConfig.customExporter) {
          const exportableData = table.getRowModel().rows.map((row) => {
            const rowData: Record<string, any> = {};
            row.getVisibleCells().forEach((cell) => {
              const cellColumnConfig = columnConfig[cell.column.id as keyof T];
              if (cellColumnConfig?.exportable !== false) {
                rowData[cell.column.id] = cellColumnConfig?.formatter
                  ? cellColumnConfig.formatter(cell.getValue())
                  : cell.getValue();
              }
            });
            return rowData;
          });

          exportConfig.customExporter(exportableData, format);
          return;
        }

        // Built-in export implementations
        switch (format) {
          case 'csv':
            exportToCSV();
            break;
          case 'excel':
            exportToExcel();
            break;
          case 'pdf':
            exportToPDF();
            break;
        }
      },
      [table, exportConfig, columnConfig]
    );

    const exportToCSV = useCallback(() => {
      const headers = table
        .getHeaderGroups()[0]
        .headers.filter((header) => {
          const headerColumnConfig = columnConfig[header.column.id as keyof T];
          return headerColumnConfig?.exportable !== false;
        })
        .map((header) => {
          const headerText =
            (header.column.columnDef.header as string) || header.id;
          return `"${headerText.replace(/"/g, '""')}"`;
        })
        .join(',');

      const csvData = table
        .getRowModel()
        .rows.map((row) =>
          row
            .getVisibleCells()
            .filter((cell) => {
              const cellColumnConfig = columnConfig[cell.column.id as keyof T];
              return cellColumnConfig?.exportable !== false;
            })
            .map((cell) => {
              const cellColumnConfig = columnConfig[cell.column.id as keyof T];
              let value = cellColumnConfig?.formatter
                ? cellColumnConfig.formatter(cell.getValue())
                : cell.getValue();

              // Handle different data types for CSV
              if (value === null || value === undefined) {
                return '';
              }
              if (typeof value === 'object') {
                value = JSON.stringify(value);
              }
              return `"${String(value).replace(/"/g, '""')}"`;
            })
            .join(',')
        )
        .join('\n');

      const fullCsv = headers + '\n' + csvData;
      const blob = new Blob([fullCsv], { type: 'text/csv;charset=utf-8;' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = exportConfig.filename || 'table-data.csv';
      a.click();
      window.URL.revokeObjectURL(url);
    }, [table, exportConfig, columnConfig]);

    const exportToExcel = useCallback(() => {
      // Simple Excel export using HTML table format
      const headers = table
        .getHeaderGroups()[0]
        .headers.filter((header) => {
          const headerColumnConfig = columnConfig[header.column.id as keyof T];
          return headerColumnConfig?.exportable !== false;
        })
        .map(
          (header) => (header.column.columnDef.header as string) || header.id
        )
        .join('\t');

      const excelData = table
        .getRowModel()
        .rows.map((row) =>
          row
            .getVisibleCells()
            .filter((cell) => {
              const cellColumnConfig = columnConfig[cell.column.id as keyof T];
              return cellColumnConfig?.exportable !== false;
            })
            .map((cell) => {
              const cellColumnConfig = columnConfig[cell.column.id as keyof T];
              const value = cellColumnConfig?.formatter
                ? cellColumnConfig.formatter(cell.getValue())
                : cell.getValue();

              if (value === null || value === undefined) return '';
              if (typeof value === 'object') return JSON.stringify(value);
              return String(value);
            })
            .join('\t')
        )
        .join('\n');

      const fullExcel = headers + '\n' + excelData;
      const blob = new Blob([fullExcel], { type: 'application/vnd.ms-excel' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = (exportConfig.filename || 'table-data').replace(
        '.csv',
        '.xls'
      );
      a.click();
      window.URL.revokeObjectURL(url);
    }, [table, exportConfig, columnConfig]);

    const exportToPDF = useCallback(() => {
      // Simple PDF export using print functionality
      const printWindow = window.open('', '_blank');
      if (!printWindow) return;

      const headers = table
        .getHeaderGroups()[0]
        .headers.filter((header) => {
          const headerColumnConfig = columnConfig[header.column.id as keyof T];
          return headerColumnConfig?.exportable !== false;
        })
        .map(
          (header) =>
            `<th>${
              (header.column.columnDef.header as string) || header.id
            }</th>`
        )
        .join('');

      const rows = table
        .getRowModel()
        .rows.map(
          (row) =>
            '<tr>' +
            row
              .getVisibleCells()
              .filter((cell) => {
                const cellColumnConfig =
                  columnConfig[cell.column.id as keyof T];
                return cellColumnConfig?.exportable !== false;
              })
              .map((cell) => {
                const cellColumnConfig =
                  columnConfig[cell.column.id as keyof T];
                const value = cellColumnConfig?.formatter
                  ? cellColumnConfig.formatter(cell.getValue())
                  : cell.getValue();

                if (value === null || value === undefined) return '<td></td>';
                if (typeof value === 'object')
                  return `<td>${JSON.stringify(value)}</td>`;
                return `<td>${String(value)}</td>`;
              })
              .join('') +
            '</tr>'
        )
        .join('');

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Table Export</title>
          <style>
            table { border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <table>
            <thead><tr>${headers}</tr></thead>
            <tbody>${rows}</tbody>
          </table>
          <script>window.print(); window.close();</script>
        </body>
        </html>
      `;

      printWindow.document.write(htmlContent);
      printWindow.document.close();
    }, [table, exportConfig, columnConfig]);

    // Handle selection changes
    useEffect(() => {
      if (enableRowSelection && onSelectionChange) {
        const selectedRows = table
          .getSelectedRowModel()
          .rows.map((row) => row.original);
        const selectedIndices = table
          .getSelectedRowModel()
          .rows.map((row) => row.index);
        onSelectionChange(selectedRows, selectedIndices);
      }
    }, [rowSelection, enableRowSelection, onSelectionChange, table]);

    // Loading state
    if (loadingConfig.enabled) {
      return (
        <div className={className}>
          {loadingConfig.skeleton ? (
            <SkeletonLoader rows={loadingConfig.rows || 5} />
          ) : (
            <div>{loadingConfig.message || 'Loading...'}</div>
          )}
        </div>
      );
    }

    // Empty state
    if (!data || data.length === 0) {
      return (
        <EmptyState>
          <Text
            textItems={[
              { text: 'No data available', options: { format: 'paragraph' } },
            ]}
          />
        </EmptyState>
      );
    }

    return (
      <TableContainer
        className={className}
        ref={tableRef}
        height={height}
        maxHeight={maxHeight}
        aria-label={ariaLabel || 'Data table'}
        aria-describedby={ariaDescription}
        role="application"
        tabIndex={0}
        onKeyDown={handleKeyDown}
        onFocus={() => {
          if (!focusedCell && table.getRowModel().rows.length > 0) {
            setFocusedCell({ rowIndex: 0, columnIndex: 0 });
            setAnnounceText('Table focused. Use arrow keys to navigate.');
          }
        }}
      >
        {/* Screen reader announcements */}
        <div
          aria-live="polite"
          aria-atomic="true"
          style={{
            position: 'absolute',
            left: '-10000px',
            width: '1px',
            height: '1px',
            overflow: 'hidden',
          }}
        >
          {announceText}
        </div>
        {/* Enhanced Global Filter and Column Controls */}
        {(enableFiltering || Object.keys(columnVisibility).length > 0) && (
          <FilterContainer>
            {enableFiltering && (
              <GlobalFilterInput
                type="text"
                placeholder="Search all columns..."
                onChange={(e) => handleGlobalFilterChange(e.target.value)}
              />
            )}

            {/* Column Visibility Toggle */}
            <ColumnVisibilityContainer>
              <ColumnVisibilityButton
                onClick={() => {
                  // Toggle column visibility dropdown
                  const dropdown = document.getElementById(
                    'column-visibility-dropdown'
                  );
                  if (dropdown) {
                    dropdown.style.display =
                      dropdown.style.display === 'none' ? 'block' : 'none';
                  }
                }}
              >
                Columns ▼
              </ColumnVisibilityButton>
              <ColumnVisibilityDropdown id="column-visibility-dropdown">
                {table.getAllColumns().map((column) => (
                  <ColumnVisibilityItem key={column.id}>
                    <input
                      type="checkbox"
                      id={`column-${column.id}`}
                      checked={column.getIsVisible()}
                      onChange={column.getToggleVisibilityHandler()}
                      aria-describedby={`column-${column.id}-label`}
                    />
                    <label
                      htmlFor={`column-${column.id}`}
                      id={`column-${column.id}-label`}
                    >
                      {(column.columnDef.header as string) || column.id}
                    </label>
                  </ColumnVisibilityItem>
                ))}
              </ColumnVisibilityDropdown>
            </ColumnVisibilityContainer>

            {/* Enhanced Export Options */}
            {exportConfig.enabled && (
              <ExportContainer>
                <ExportButton onClick={() => exportData('csv')}>
                  Export CSV
                </ExportButton>
                {exportConfig.formats?.includes('excel') && (
                  <ExportButton onClick={() => exportData('excel')}>
                    Export Excel
                  </ExportButton>
                )}
                {exportConfig.formats?.includes('pdf') && (
                  <ExportButton onClick={() => exportData('pdf')}>
                    Export PDF
                  </ExportButton>
                )}
              </ExportContainer>
            )}
          </FilterContainer>
        )}

        {/* Bulk Actions */}
        {enableRowSelection &&
          bulkActions.length > 0 &&
          Object.keys(rowSelection).length > 0 && (
            <BulkActionsContainer>
              {bulkActions.map((action) => (
                <BulkActionButton
                  key={action.id}
                  variant={action.variant}
                  onClick={() => {
                    const selectedRows = table
                      .getSelectedRowModel()
                      .rows.map((row) => row.original);
                    const selectedIndices = table
                      .getSelectedRowModel()
                      .rows.map((row) => row.index);
                    action.onClick(selectedRows, selectedIndices);
                  }}
                  disabled={action.disabled?.(
                    table.getSelectedRowModel().rows.map((row) => row.original)
                  )}
                >
                  {action.label}
                </BulkActionButton>
              ))}
            </BulkActionsContainer>
          )}

        {/* Enhanced Table */}
        <TableWrapper border={tableBorder}>
          <StyledTable>
            <Thead>
              {table.getHeaderGroups().map((headerGroup) => (
                <Tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <Th key={header.id} style={{ width: header.getSize() }}>
                      <HeaderContent>
                        <HeaderText
                          sortable={header.column.getCanSort()}
                          onClick={header.column.getToggleSortingHandler()}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                        </HeaderText>
                        {header.column.getCanSort() && (
                          <SortIndicator>
                            {{
                              asc: ' 🔼',
                              desc: ' 🔽',
                            }[header.column.getIsSorted() as string] ?? ' ↕️'}
                          </SortIndicator>
                        )}
                        {enableColumnResizing &&
                          header.column.getCanResize() && (
                            <ResizeHandle
                              onMouseDown={header.getResizeHandler()}
                              onTouchStart={header.getResizeHandler()}
                            />
                          )}
                      </HeaderContent>
                      {header.column.getCanFilter() && (
                        <FilterInput
                          type="text"
                          placeholder="Filter..."
                          value={
                            (header.column.getFilterValue() as string) ?? ''
                          }
                          onChange={(e) =>
                            header.column.setFilterValue(e.target.value)
                          }
                          onClick={(e) => e.stopPropagation()}
                        />
                      )}
                    </Th>
                  ))}
                  {rowActions.length > 0 && (
                    <Th>
                      <Text
                        textItems={[
                          { text: 'Actions', options: { format: 'heading' } },
                        ]}
                      />
                    </Th>
                  )}
                </Tr>
              ))}
            </Thead>
            <Tbody>
              {shouldUseVirtualScrolling ? (
                <VirtualScrollContainer
                  height={containerHeight - 100}
                  onScroll={virtualScrolling.handleScroll}
                >
                  <VirtualScrollContent
                    totalHeight={virtualScrolling.totalHeight}
                  >
                    <VirtualScrollViewport
                      translateY={virtualScrolling.offsetY}
                      height={
                        virtualScrolling.visibleItems.length *
                        (virtualScrollConfig.itemHeight || 50)
                      }
                    >
                      {table
                        .getRowModel()
                        .rows.slice(
                          virtualScrolling.startIndex,
                          virtualScrolling.endIndex + 1
                        )
                        .map((row, virtualIndex) => {
                          const actualIndex =
                            virtualScrolling.startIndex + virtualIndex;
                          return (
                            <Tr
                              key={row.id}
                              onClick={
                                onRowClick
                                  ? () => onRowClick(row.original, actualIndex)
                                  : undefined
                              }
                              onDoubleClick={
                                onRowDoubleClick
                                  ? () =>
                                      onRowDoubleClick(
                                        row.original,
                                        actualIndex
                                      )
                                  : undefined
                              }
                              clickable={!!onRowClick || !!onRowDoubleClick}
                              selected={
                                enableRowSelection && row.getIsSelected()
                              }
                              style={{
                                height: virtualScrollConfig.itemHeight || 50,
                              }}
                            >
                              {row
                                .getVisibleCells()
                                .map((cell: any, cellIndex: number) => (
                                  <Td
                                    key={cell.id}
                                    tabIndex={
                                      focusedCell?.rowIndex === actualIndex &&
                                      focusedCell?.columnIndex === cellIndex
                                        ? 0
                                        : -1
                                    }
                                    role="gridcell"
                                    aria-selected={
                                      enableRowSelection
                                        ? row.getIsSelected()
                                        : undefined
                                    }
                                    onClick={() =>
                                      setFocusedCell({
                                        rowIndex: actualIndex,
                                        columnIndex: cellIndex,
                                      })
                                    }
                                  >
                                    {flexRender(
                                      cell.column.columnDef.cell,
                                      cell.getContext()
                                    )}
                                  </Td>
                                ))}
                              {rowActions.length > 0 && (
                                <Td>
                                  <RowActionsContainer>
                                    {rowActions.map(
                                      (action) =>
                                        !action.hidden?.(row.original) && (
                                          <RowActionButton
                                            key={action.id}
                                            variant={action.variant}
                                            onClick={() =>
                                              action.onClick(
                                                row.original,
                                                actualIndex
                                              )
                                            }
                                            disabled={action.disabled?.(
                                              row.original
                                            )}
                                            title={action.label}
                                          >
                                            {action.label}
                                          </RowActionButton>
                                        )
                                    )}
                                  </RowActionsContainer>
                                </Td>
                              )}
                            </Tr>
                          );
                        })}
                    </VirtualScrollViewport>
                  </VirtualScrollContent>
                </VirtualScrollContainer>
              ) : (
                table.getRowModel().rows.map((row) => (
                  <Tr
                    key={row.id}
                    onClick={
                      onRowClick
                        ? () => onRowClick(row.original, row.index)
                        : undefined
                    }
                    onDoubleClick={
                      onRowDoubleClick
                        ? () => onRowDoubleClick(row.original, row.index)
                        : undefined
                    }
                    clickable={!!onRowClick || !!onRowDoubleClick}
                    selected={enableRowSelection && row.getIsSelected()}
                  >
                    {row.getVisibleCells().map((cell, cellIndex) => (
                      <Td
                        key={cell.id}
                        tabIndex={
                          focusedCell?.rowIndex === row.index &&
                          focusedCell?.columnIndex === cellIndex
                            ? 0
                            : -1
                        }
                        role="gridcell"
                        aria-selected={
                          enableRowSelection ? row.getIsSelected() : undefined
                        }
                        onClick={() =>
                          setFocusedCell({
                            rowIndex: row.index,
                            columnIndex: cellIndex,
                          })
                        }
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </Td>
                    ))}
                    {rowActions.length > 0 && (
                      <Td>
                        <RowActionsContainer>
                          {rowActions.map(
                            (action) =>
                              !action.hidden?.(row.original) && (
                                <RowActionButton
                                  key={action.id}
                                  variant={action.variant}
                                  onClick={() =>
                                    action.onClick(row.original, row.index)
                                  }
                                  disabled={action.disabled?.(row.original)}
                                  title={action.label}
                                >
                                  {action.label}
                                </RowActionButton>
                              )
                          )}
                        </RowActionsContainer>
                      </Td>
                    )}
                  </Tr>
                ))
              )}
            </Tbody>
          </StyledTable>
        </TableWrapper>

        {/* Enhanced Pagination */}
        {enablePagination && (
          <PaginationWrapper>
            <PaginationButtons>
              <PaginationButton
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
              >
                {'<<'}
              </PaginationButton>
              <PaginationButton
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                {'<'}
              </PaginationButton>
              <PaginationButton
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                {'>'}
              </PaginationButton>
              <PaginationButton
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
              >
                {'>>'}
              </PaginationButton>
            </PaginationButtons>
            <PaginationInfo>
              <PageInfo>
                Page {table.getState().pagination.pageIndex + 1} of{' '}
                {table.getPageCount()}
              </PageInfo>
              <PageSizeSelect
                value={table.getState().pagination.pageSize}
                onChange={(e) => {
                  table.setPageSize(Number(e.target.value));
                }}
                aria-label="Rows per page"
                title="Rows per page"
              >
                {pageSizeOptions.map((size) => (
                  <option key={size} value={size}>
                    Show {size}
                  </option>
                ))}
              </PageSizeSelect>
            </PaginationInfo>
          </PaginationWrapper>
        )}

        {/* Enhanced Info */}
        {enableInfoText && (
          <InfoText>
            Showing {table.getRowModel().rows.length} of{' '}
            {table.getPreFilteredRowModel().rows.length} rows
            {enableRowSelection && Object.keys(rowSelection).length > 0 && (
              <span> ({Object.keys(rowSelection).length} selected)</span>
            )}
          </InfoText>
        )}
      </TableContainer>
    );
  }
);
