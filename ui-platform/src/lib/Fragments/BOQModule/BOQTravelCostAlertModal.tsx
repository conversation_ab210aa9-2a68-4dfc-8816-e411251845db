import React from 'react';
import styled from 'styled-components';
import { TextButton } from '../../Components/Buttons/TextButton/TextButton';
import { AlertPopup } from '../../Components/AlertPopup/Popup';
import { FormButton, Heading, Icon } from '../../Components';

interface ActionButtonsProps {
  travelDistance: number;
  closeModal: () => void;
  submitBOQ: (boqDetails: {
    description: string;
    quantity: number;
    unitPrice: number;
    optionalItem: boolean;
  }) => void;
}

const AlertMask = styled.div`
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.45);
  display: grid;
  align-items: center;
  justify-content: center;
  gap: ${(props) => props.theme.GapXxl};
  z-index: 10;
  color: ${(props) => props.theme.ColorsIconColorPrimary};
`;

const Button = styled(TextButton)`
  display: grid;
  grid-auto-flow: column;
  gap: 1rem;
`;

const ModalContent = styled(AlertPopup)`
  width: auto;
  height: auto;
  min-width: 336px;
  min-height: 234px;
  display: grid;
  justify-items: center;
  grid-auto-flow: row;
  gap: ${(props) => props.theme.GapLg};
  padding: 4rem;
  position: relative;
  overflow: auto;
  z-index: 20;

  > div:not(:first-child) {
    width: 100%;
  }
`;

const BodyText = styled.p`
  text-align: center;
  margin: 0;
`;

const ModalHeading = styled(Heading)`
  margin: unset;
`;

const TopRightButtonWrapper = styled.div`
  position: absolute;
  top: 15px;
  right: 15px;
`;

const ButtonContainer = styled.div`
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(1, 1fr);
  gap: 1rem;
`;

const BOQTravelCostAlertModal: React.FC<ActionButtonsProps> = ({
  travelDistance,
  closeModal,
  submitBOQ,
}) => {
  const handleYesClick = () => {
    const unitPrice = 6.20;
    submitBOQ({
      description: 'Travel Fee (Every km Over 60km) - per km',
      quantity: travelDistance,
      unitPrice,
      optionalItem: true,
    });
  };

  return (
    <AlertMask>
      <ModalContent type={'warning'}>
        <TopRightButtonWrapper>
          <FormButton onClick={closeModal} width="35px" height="35px">
            <Icon type="close" size={20} />
          </FormButton>
        </TopRightButtonWrapper>
        <ModalHeading level={1} type={'page-heading'}>
          Charge for travel costs?
        </ModalHeading>
        <BodyText>
          This job is eligible to charge travel costs for {travelDistance}km.
          <br />
          Do you want to include travel costs on this quotation?
        </BodyText>
        <ButtonContainer>
          <Button btnValue="No" onClick={closeModal} />
          <Button btnValue="Yes" onClick={handleYesClick} />
        </ButtonContainer>
      </ModalContent>
    </AlertMask>
  );
};

export default BOQTravelCostAlertModal;
