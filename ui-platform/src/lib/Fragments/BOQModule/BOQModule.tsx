import React from 'react';
import {
  BOQItemsResponse,
  BOQTampleteResponse,
  JobFeeOptions,
} from '../../Engine/helpers/BOQ-utils';
import { CompilingQuote } from './state_85/CompilingQuote';
import { BOQAllCompulsoryQuote } from './state_85/BOQAllCompulsoryQuote';
import { UploadNewInvoice } from './state_47/UploadNewInvoice';
import {
  PreselectedOptionalItem,
  PreselectedCompulsoryItem,
} from '../BOQComplete/BOQComplete';

export interface BOQModuleProps {
  state: number;
  screenName?: string;
  boqItems: BOQItemsResponse;
  boqTemplates: BOQTampleteResponse[];
  templateName?: string;
  travelDistance?: number;
  createCustomItemButton?: boolean;
  excessAmount?: number;
  vat?: number;
  initialValue?: string;
  invoiceHeading?: string;
  preselectedOptionalItem?: PreselectedOptionalItem;
  preselectedCompulsoryItem?: PreselectedCompulsoryItem;
  jobFeeOptions?: JobFeeOptions;
}

export function BOQModule({
  state,
  screenName,
  boqItems = { items: [] },
  boqTemplates = [],
  templateName,
  travelDistance,
  createCustomItemButton,
  excessAmount,
  jobFeeOptions,
  vat,
  initialValue,
  invoiceHeading,
  preselectedOptionalItem,
  preselectedCompulsoryItem,
}: BOQModuleProps) {
  const compilingStates = [
    25, 46, 66, 67, 69, 80, 85, 91, 94, 105, 171, 206, 289, 326,
  ];
  const uploadInvoiceStates = [47, 293, 397];

  if (compilingStates.includes(state)) {
    return (
      <CompilingQuote
        boqItems={boqItems}
        boqTemplates={boqTemplates}
        templateName={templateName}
        travelDistance={travelDistance}
        createCustomItemButton={createCustomItemButton}
        excessAmount={excessAmount}
        jobFeeOptions={jobFeeOptions}
        vat={vat}
        initialValue={initialValue}
        invoiceHeading={invoiceHeading}
        preselectedOptionalItem={preselectedOptionalItem}
      />
    );
  }
  if (uploadInvoiceStates.includes(state)) {
    return (
      <UploadNewInvoice
        boqItems={boqItems}
        travelDistance={travelDistance}
        createCustomItemButton={createCustomItemButton}
        excessAmount={excessAmount}
        jobFeeOptions={jobFeeOptions}
        vat={vat}
        initialValue={initialValue}
        invoiceHeading={invoiceHeading}
        preselectedOptionalItem={preselectedOptionalItem}
      />
    );
  }
  if (state === 26) {
    return (
      <BOQAllCompulsoryQuote
        boqItems={boqItems}
        travelDistance={travelDistance}
        preselectedCompulsoryItem={preselectedCompulsoryItem}
        excessAmount={excessAmount}
        jobFeeOptions={jobFeeOptions}
        vat={vat}
        initialValue={initialValue}
        invoiceHeading={invoiceHeading}
      />
    );
  }
  return <h1>BOQModule</h1>;
}
