import React, { useMemo } from 'react';

import { Item } from '../../../Components/BOQ/LineItemModify/LineItemModify';
import {
  BOQItemsResponse,
  BOQTampleteResponse,
  getAllBOQItemsAsOptionalItems,
  getBillingCompulsoryItemsForMultipleTemplates,
  getBillingCompulsoryItemsForSingleTemplate,
  getBillingOptionalItemsForMultipleTemplates,
  getBillingOptionalItemsForSingleTemplate,
  JobFeeOptions,
} from '../../../Engine/helpers/BOQ-utils';
import {
  BOQComplete,
  PreselectedOptionalItem,
} from '../../BOQComplete/BOQComplete';
import { samplePreselectedOptionalItem } from '../sample-data';

interface CompilingQuoteProps {
  boqItems: BOQItemsResponse;
  boqTemplates: BOQTampleteResponse[];
  templateName?: string;
  travelDistance?: number;
  createCustomItemButton?: boolean;
  excessAmount?: number;
  jobFeeOptions?: JobFeeOptions;
  vat?: number;
  invoiceHeading?: string;
  initialValue?: string;
  preselectedOptionalItem?: PreselectedOptionalItem;
  _formContext?: any;
}

const handleItemsChange = (updatedItems: Item[]) => {
  console.log('Updated items:', updatedItems);
};

export function CompilingQuote({
  boqItems = { items: [] },
  boqTemplates = [],
  templateName,
  travelDistance,
  createCustomItemButton,
  excessAmount,
  jobFeeOptions,
  vat,
  initialValue,
  invoiceHeading,
  preselectedOptionalItem,
  _formContext,
}: CompilingQuoteProps) {
  const selectedTemplateName =
    templateName || _formContext?.getValues('selectedTemplateName') || '';
  //
  const isMultipleTemplates = useMemo(
    () => boqTemplates.length > 1,
    [boqTemplates]
  );

  // Get the compulsory items
  const compulsoryItems = useMemo(() => {
    if (isMultipleTemplates) {
      return getBillingCompulsoryItemsForMultipleTemplates(
        boqTemplates,
        boqItems,
        selectedTemplateName
      );
    } else {
      return getBillingCompulsoryItemsForSingleTemplate(boqTemplates, boqItems);
    }
  }, [isMultipleTemplates, boqItems, boqTemplates, selectedTemplateName]);

  // Get the Optional Items
  const compulsoryAsOptionalItems = useMemo(() => {
    if (isMultipleTemplates) {
      return getBillingOptionalItemsForMultipleTemplates(
        boqTemplates,
        boqItems,
        selectedTemplateName
      );
    } else {
      return getBillingOptionalItemsForSingleTemplate(boqTemplates, boqItems);
    }
  }, [isMultipleTemplates, boqItems, boqTemplates, selectedTemplateName]);

  return (
    <BOQComplete
      travelDistance={travelDistance}
      preselectedOptionalItem={preselectedOptionalItem}
      createCustomItemButton={createCustomItemButton}
      excessAmount={excessAmount}
      jobFeeOptions={jobFeeOptions}
      vat={vat}
      initialValue={initialValue}
      invoiceHeading={invoiceHeading}
      lineItemsTableProps={{
        compulsoryItems: compulsoryAsOptionalItems,
        optionalItems: getAllBOQItemsAsOptionalItems(boqItems),
        columnNames: {
          description: 'Description',
          quantity: 'Quantity',
          unitPrice: 'Unit Price',
          total: 'Total',
        },
        onItemsChange: handleItemsChange,
      }}
      invoiceSummaryNotesProps={{
        notes: '',
      }}
      invoiceSummaryProps={{
        // vat: 15,
        subTotal: 0,
      }}
    />
  );
}
