import React from 'react';

import { Item } from '../../../Components/BOQ/LineItemModify/LineItemModify';
import {
  BOQItemsResponse,
  getAllBOQItemsAsOptionalItems,
  JobFeeOptions,
} from '../../../Engine/helpers/BOQ-utils';
import { BOQComplete, PreselectedOptionalItem } from '../../BOQComplete/BOQComplete';

interface UploadNewInvoiceProps {
  boqItems: BOQItemsResponse;
  travelDistance?: number;
  createCustomItemButton?: boolean;
  excessAmount?: number;
  jobFeeOptions?: JobFeeOptions;
  vat?: number;
  preselectedOptionalItem?: PreselectedOptionalItem;
  _formContext?: any;
  initialValue?: string;
  invoiceHeading?: string;
}

const handleItemsChange = (updatedItems: Item[]) => {
  console.log('Updated items:', updatedItems);
};

export function UploadNewInvoice({
  boqItems = { items: [] },
  travelDistance,
  createCustomItemButton,
  excessAmount,
  jobFeeOptions,
  vat,
  initialValue,
  invoiceHeading,
  preselectedOptionalItem,
}: UploadNewInvoiceProps) {
  // Display all items without separation
  // const allItems: Item[] = boqItems.items; // build type error fix

  return (
    <BOQComplete
      travelDistance={travelDistance}
      createCustomItemButton={createCustomItemButton}
      preselectedOptionalItem={preselectedOptionalItem}
      excessAmount={excessAmount}
      jobFeeOptions={jobFeeOptions}
      vat={vat}
      initialValue={initialValue}
      invoiceHeading={invoiceHeading}
      lineItemsTableProps={{
        compulsoryItems: [], // Using all items here
        optionalItems: getAllBOQItemsAsOptionalItems(boqItems), // No optional/compulsory distinction
        columnNames: {
          description: 'Description',
          quantity: 'Quantity',
          unitPrice: 'Unit Price',
          total: 'Total',
        },
        onItemsChange: handleItemsChange,
      }}
      invoiceSummaryNotesProps={{
        notes: '',
      }}
      invoiceSummaryProps={{
        // vat: 15,
        subTotal: 0,
      }}
    />
  );
}
