import { StoryObj, Meta } from '@storybook/react/';
import { InvoiceQuery } from './InvoiceQuery';

const meta: Meta<typeof InvoiceQuery> = {
  component: InvoiceQuery,
  title: 'Fragments/InvoiceQuery',
};
export default meta;
type Story = StoryObj<typeof InvoiceQuery>;

const data: any = {
  'Reason for invoice query 1': 'TESTINGs TESTING',
  'Allocated team leader': 'Team leader not found.',
  'Claim Type': 'Geyser + Damages',
};

export const Default: Story = {
  args: {
    keyValueListData: data,
    documentUrl: 'https://s28.q4cdn.com/392171258/files/doc_downloads/test.pdf',
    invoiceInfoData: [
      'There is a query on the invoice',
      'Please read below and address the query',
    ],
  },
};

export const B64: Story = {
  args: {
    keyValueListData: data,
    documentUrl:
      '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',
    invoiceInfoData: [
      'There is a query on the invoice',
      'Please read below and address the query',
    ],
    isBase64: true,
  },
};

export const WithoutFiles: Story = {
  args: {
    keyValueListData: data,
    invoiceInfoData: [
      'There is a query on the invoice',
      'Please read below and address the query',
    ],
    isBase64: true,
  },
};
