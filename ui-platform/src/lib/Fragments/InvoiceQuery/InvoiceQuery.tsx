import React from 'react';
import { InvoiceInfoCard } from '../../Components/Cards/InvoiceInfoCard/InvoiceInfoCard';
import { KeyValueList } from '../KeyValueList/KeyValueList';

import styled from 'styled-components';
import { PDFPreview } from '../PDFviewer/PDFviewer';

export const StyleContainerWrapper = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  justify-items: center;
  padding: 20px;
  height: 70vh;
`;

const StyledContainer = styled.div`
  display: grid;
  grid-template-rows: auto 1fr;
  justify-items: start;
  gap: 0;
`;

const PDFContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const PDFPreviewStyled = styled(PDFPreview)`
  flex: 1; /* Ensure the PDFPreview takes the remaining space */
`;

interface InvoiceQueryProps {
  keyValueListData: Record<string, string | number | boolean>;
  documentUrl: string;
  invoiceInfoData: string[];
  fallbackMessage: string;
  isBase64?: boolean;
}

export const InvoiceQuery: React.FC<InvoiceQueryProps> = ({
  keyValueListData,
  documentUrl,
  invoiceInfoData,
  fallbackMessage,
  isBase64,
}) => {
  return (
    <StyleContainerWrapper>
      <PDFContainer>
        <PDFPreviewStyled
          fallbackMessage={fallbackMessage}
          isBase64={isBase64}
          documents={documentUrl ? [{ documentUrl }] : []}
        />
      </PDFContainer>
      <StyledContainer>
        <InvoiceInfoCard data={invoiceInfoData} />
        <KeyValueList
          numbering={false}
          heading={''}
          data={keyValueListData}
          width={'auto'}
          itemMargin={'10px'}
          align={'left'}
          colouredHeading={{ headingString: '', headingColour: 'default' }}
          colour={'default'}
          size={'medium'}
          textTransform={'default'}
        />
      </StyledContainer>
    </StyleContainerWrapper>
  );
};
