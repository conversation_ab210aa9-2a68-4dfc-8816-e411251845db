import React, { useEffect, useMemo, useState } from 'react';
import { workflowAppConfig } from '../../workflow-app-config';
import {
  StateShell,
} from '../../../../Shells';
import { Pagination } from '../../../../Fragments/Pagination/Pagination';
import styled from 'styled-components';
import { ScrollableContent } from '../../../../Components/Scrollbar/Scrollbar';
import { AlertModal, useListPagination2 } from '../../../../Components';
import { JobCardList } from '../../../../Fragments';
import { StaffMember } from '../../../../Auth';
import { ActionConfig, AllInfo, ModalState, setModalState, StateConfig, useFilteringEngineStore, useModalStore } from '../../../../Engine';
import { createPortal } from 'react-dom';
import { componentMap } from '../../../../component-map';
import { FormProvider, useForm } from 'react-hook-form';


const ModuleContent = styled.div`
  background: ${(props) => props.theme.ColorsBackgroundModule};
  padding-top: 2rem;
  padding-left: 1rem;
  padding-right: 1rem;
  box-sizing: border-box;
  border-radius: 0 7px 7px 7px;
  grid-area: Module-Content;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
`;

const FiltersWrapper = styled.div`
  position: relative;
  z-index: 2;
`;

const JobListWrapper = styled.div`
  position: relative;
  z-index: 1;
  margin-top: 1rem;
`;

const ViewShellPagination = styled(Pagination)`
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
`;

const Content = styled(ScrollableContent)<{
  items?: any[];
  scrollable?: boolean;
}>`
  max-height: 100% !important;
  padding-right: ${(props) => props.theme.SpacingXl};
  height: calc(100vh - 2rem - 72px);

  ${(props) =>
    props?.scrollable || (props?.items && props?.items.length > 0)
      ? 'padding-bottom: 4rem; box-sizing: border-box'
      : ''};
  ${(props) => (!props?.scrollable ? 'overflow: hidden;' : '')};
`;



interface Props {
  jobs: any[];
  staffMember: StaffMember;
  allInfo: Partial<AllInfo>;
  submit?: any;
  fetcher?: any;
  itemsPerPage?: number;
  jobCardNumberPrefix: string;
  searchUrl?: string;
  LinkRouter: any;
  actionPanels: StateConfig['actionPanels'];
  getMenuItems: (job: any) => {icon: string; label: string; path: string; onClick?: ActionConfig[];}[];
  callClientAction: (config: ActionConfig) => void;
}

export function JobsDetailedView({ LinkRouter, actionPanels, jobs, itemsPerPage, jobCardNumberPrefix, getMenuItems, callClientAction, submit, fetcher }: Props) {
   //
    const getFilteredData = useFilteringEngineStore(state => state.getFilteredData);
    const filterFunctions = useFilteringEngineStore(state => state.filterFunctions);
    const toggleSort = useFilteringEngineStore(state => state.toggleSort);
    const getSortedData = useFilteringEngineStore(state => state.getSortedData);

    const filteredJobs = useMemo(() => {
      console.log("Filter Functions Count", filterFunctions.length);
      if (filterFunctions.length === 1 && filterFunctions[0].name === 'search_results') {
        return filterFunctions[0]?.getSearchResults?.() || [];
      }
      return getFilteredData(jobs);
    }, [jobs, getFilteredData, filterFunctions.length]);

    const sortedJobs = useMemo(() => {
      if (toggleSort) {
        return getSortedData(filteredJobs);
      }
      return filteredJobs;
    }, [toggleSort, filteredJobs]);
    
    
  const { pages, currentPage, pageItems, sortItemsBy, ...rest } = useListPagination2({
    items: sortedJobs || [],
    itemsPerPage: itemsPerPage || 10,
  });


   const config: StateConfig = {
      title: { 
          template: '', 
          
      },
      fetchCalls: [],
      onEnter: [],
      onLeave: [],
      defaultScreen: '',
      screens: {},
      actionPanels,
  };

   // FORM HANDLER
      const methods = useForm({
        mode: 'onSubmit',
        defaultValues: {},
      });
  
      // Reset Form
      useEffect(() => {
        console.log('resetting form on job detailed view!');
        methods.reset({});
      }, []);

    const currentModalConfig = useModalStore(
      (state: Partial<ModalState>) => state.currentModalConfig
    );
  
  return (
    <>
      <StateShell
        callClientAction={callClientAction}
        stateConfig={config}
        clientDataObject={{}}
        fakeUseNavigation={{ state: 'idle' }}
      >
         <FormProvider {...methods}>
      {currentModalConfig?.display &&
        createPortal(
          <AlertModal
            componentMap={componentMap}
            setModalState={setModalState}
            {...{
              heading: currentModalConfig.heading,
              headingLevel: currentModalConfig.headingLevel,
              headingType: currentModalConfig.headingType,
              type: currentModalConfig.type,
              display: currentModalConfig.display,
              layout: currentModalConfig.layout,
              onEnter: currentModalConfig.onEnter,
              onLeave: currentModalConfig.onLeave,
              fetchCalls: currentModalConfig.fetchCalls,
              fragments: currentModalConfig.fragments,
              navs: currentModalConfig.navs,
            }}
            onClose={() => {
              currentModalConfig?.onClose &&
                callClientAction(currentModalConfig.onClose);
              setModalState({ display: false });
            }}
            submit={submit as any}
            fetcher={fetcher as any}
            callClientAction={callClientAction}
          />,
          document.body
        )}
      </FormProvider>
        <ModuleContent data-testid="workflow-view-shell-module-content">
          <Content
            data-testid="workflow-view-shell-content"
            items={pageItems}
            scrollable={true}
          >
            <JobListWrapper>
              <JobCardList LinkRouter={LinkRouter} jobs={pageItems} jobCardNumberPrefix={jobCardNumberPrefix} getMenuItems={getMenuItems} callClientAction={callClientAction}/>
            </JobListWrapper>
          </Content>
          <ViewShellPagination
            pages={pages}
            currentPage={currentPage}
            {...rest}
          />
        </ModuleContent>
      </StateShell>
    </>
  );
}
