export interface BOQItemsResponse {
  items: {
    id: number;
    is_after_hours: boolean;
    is_custom_items: boolean;
    item_type: number;
    measurement: string;
    name: string;
    price: number;
    skill_categories: any[];
    year: number;
  }[];
}

export interface BOQTampleteResponse {
  allow_custom_items: boolean;
  claim_type: any[];
  compulsory_items: any[];
  id: number;
  includes_after_hours_items: boolean;
  name: string;
  optional_items: any[];
  skills: any[];
  type: string;
}

export interface BillingItem {
  id: number;
  description: string;
  quantity: number;
  optionalItem: boolean;
  unitPrice: number;
}

export interface JobFeeOptions {
  min: number;
  max: number;
  percentage: number;
}

export const getBillingCompulsoryItemsForSingleTemplate = (
  boqTampletes: BOQTampleteResponse[] = [],
  itemsContainer: BOQItemsResponse = { items: [] }
) => {
  const CompulsoryItemIds = boqTampletes[0]
    ? boqTampletes[0]?.compulsory_items
    : [];
  const boqItemsMap = itemsContainer.items.reduce((acc, item) => {
    return { ...acc, [item.id]: item };
  }, {});
  const CompulsoryItems = CompulsoryItemIds.map((id) => {
    return (boqItemsMap as any)[id];
  })
    .filter((item) => item !== undefined)
    .map((item) => {
      return {
        id: item.id,
        description: item?.name,
        quantity: 1,
        optionalItem: false,
        unitPrice: item?.price,
      };
    });
  return CompulsoryItems;
};

export const getBillingOptionalItemsForSingleTemplate = (
  boqTampletes: BOQTampleteResponse[] = [],
  boqItems: BOQItemsResponse
) => {
  const OptionsItemIds = boqTampletes[0] ? boqTampletes[0]?.compulsory_items : [];
  const boqItemsMap = boqItems.items.reduce((acc, item) => {
    return { ...acc, [item.id]: item };
  }, {});
  const OptionalItems = OptionsItemIds.map((id) => {
    return (boqItemsMap as any)[id];
  })
    .filter((item) => item !== undefined)
    .map((item) => {
      return {
        id: item.id,
        description: item?.name,
        quantity: 1,
        optionalItem: true,
        unitPrice: item?.price,
      };
    });
  return OptionalItems;
};

// FOR MULTIPLE TEMPLATES

export const getBillingCompulsoryItemsForMultipleTemplates = (
  boqTemplates: BOQTampleteResponse[],
  boqItems: BOQItemsResponse,
  templateName: string
): BillingItem[] => {
  return getBillingItemsByType(
    boqTemplates,
    boqItems,
    templateName,
    'compulsory'
  );
};

export const getBillingOptionalItemsForMultipleTemplates = (
  boqTemplates: BOQTampleteResponse[],
  boqItems: BOQItemsResponse,
  templateName: string
): BillingItem[] => {
  return getBillingItemsByType(
    boqTemplates,
    boqItems,
    templateName,
    'compulsoryAsOptional'
  );
};

const getBillingItemsByType = (
  boqTemplates: BOQTampleteResponse[],
  boqItems: BOQItemsResponse,
  templateName: string,
  itemType: 'compulsory' | 'compulsoryAsOptional'
): BillingItem[] => {
  // Find the selected template by name
  const selectedTemplate = boqTemplates.find(
    (template) => template.name === templateName
  );

  if (!selectedTemplate) {
    throw new Error(`Template with name "${templateName}" not found.`);
  }

  // Create a map of BOQ items for quick lookup
  const boqItemsMap = boqItems.items.reduce(
    (acc: { [id: string]: BOQItemsResponse['items'][0] }, boqItem) => {
      acc[boqItem.id] = boqItem;
      return acc;
    },
    {}
  );

  // Determine the list of item IDs based on the type
  const itemIds =
    itemType === 'compulsoryAsOptional'
      ? selectedTemplate.compulsory_items
      : selectedTemplate.optional_items;

  // Map item IDs to BillingItems
  return itemIds
    .map((id) => boqItemsMap[id])
    .filter((boqItem: BOQItemsResponse['items'][0]) => boqItem !== undefined)
    .map((boqItem) => ({
      id: boqItem.id,
      description: boqItem.name,
      quantity: 1,
      optionalItem: itemType === 'compulsoryAsOptional',
      unitPrice: boqItem.price,
    }));
};

export const getAllBOQItemsAsOptionalItems = (
  boqItems: BOQItemsResponse
): BillingItem[] => {
  return boqItems.items.map((item) => ({
    id: item.id,
    description: item.name,
    quantity: 1,
    optionalItem: true,
    unitPrice: item.price,
  }));
};

export const getAllBOQItemsAsCompulsoryItems = (
  boqItems: BOQItemsResponse
): BillingItem[] => {
  return boqItems.items.map((item) => ({
    id: item.id,
    description: item.name,
    quantity: 1,
    optionalItem: true,
    unitPrice: item.price,
  }));
};
