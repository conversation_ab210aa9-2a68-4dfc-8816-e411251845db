import React from 'react';
import { ActionPanelConfig } from '../models/action-panel.config';
import { ActionConfig } from '../models/action.config';
import { ClientActionPanelLayout } from './ClientActionPanelLayout';
import { ClientDynamicScreenLoader } from './ClientDynamicScreenLoader';

interface Props {
  actionPanelConfig: ActionPanelConfig;
  componentMap: any;
  submit?: any;
  fetcher?: any;
  clientDataObject?: { [id: string]: any };
  callClientAction: (config: ActionConfig) => void;
}

export function ActionPanelRenderer({
  actionPanelConfig,
  componentMap,
  submit,
  fetcher,
  callClientAction,
}: Props) {
  return (
    <ClientActionPanelLayout callClientAction={callClientAction} actionPanelConfig={actionPanelConfig}>
      <ClientDynamicScreenLoader
        callClientAction={callClientAction}
        config={actionPanelConfig}
        componentMap={componentMap}
        {...{ submit, fetcher }}
      />
    </ClientActionPanelLayout>
  );
  // return null;
}
