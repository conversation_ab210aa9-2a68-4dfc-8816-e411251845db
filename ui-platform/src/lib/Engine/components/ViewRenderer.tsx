import React from 'react';
import { ActionConfig } from '../models';
import { ViewConfig } from '../models/view.config';
import { ClientDynamicScreenLoader } from './ClientDynamicScreenLoader';
import { ClientViewLayout } from './ClientViewLayout';

interface Props {
  viewConfig: ViewConfig;
  clientDataObject: { [id: string]: any };
  componentMap: any;
  submit: any;
  fetcher: any;
  callClientAction: (config: ActionConfig) => void;
}

export function ViewRenderer({
  viewConfig,
  clientDataObject,
  componentMap,
  submit,
  fetcher,
  callClientAction,
}: Props) {
  return (
    <ClientViewLayout
      callClientAction={callClientAction}
      clientDataObject={clientDataObject}
      viewConfig={viewConfig}
    >
      <ClientDynamicScreenLoader
        callClientAction={callClientAction}
        config={viewConfig}
        componentMap={componentMap}
        {...{ submit, fetcher }}
      />
    </ClientViewLayout>
  );
}
